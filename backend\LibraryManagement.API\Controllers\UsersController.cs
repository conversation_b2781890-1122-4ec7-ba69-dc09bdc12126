using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using LibraryManagement.Core.Interfaces;
using LibraryManagement.Core.Entities;
using LibraryManagement.Application.DTOs;
using LibraryManagement.Core.Enums;

namespace LibraryManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous] // Allow anonymous access for all endpoints
public class UsersController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IAuthService _authService;

    public UsersController(IUnitOfWork unitOfWork, IAuthService authService)
    {
        _unitOfWork = unitOfWork;
        _authService = authService;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<UserManagementDto>>> GetUsers()
    {
        var users = await _unitOfWork.Users.GetAllAsync();

        var userDtos = users.Select(user => new UserManagementDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            Role = user.Role,
            RoleName = GetRoleName(user.Role),
            IsActive = user.IsActive,
            EmailVerified = user.EmailVerified,
            EmailVerifiedAt = user.EmailVerifiedAt,
            EmailVerificationMethod = user.EmailVerificationMethod,
            EmailVerifiedByName = user.EmailVerifiedBy?.FullName,
            LastLoginDate = user.LastLoginDate,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        }).OrderBy(u => u.Role).ThenBy(u => u.LastName).ToList();

        return Ok(userDtos);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<UserManagementDto>> GetUser(int id)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        var userDto = new UserManagementDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            Role = user.Role,
            RoleName = GetRoleName(user.Role),
            IsActive = user.IsActive,
            EmailVerified = user.EmailVerified,
            EmailVerifiedAt = user.EmailVerifiedAt,
            EmailVerificationMethod = user.EmailVerificationMethod,
            EmailVerifiedByName = user.EmailVerifiedBy?.FullName,
            LastLoginDate = user.LastLoginDate,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        };

        return Ok(userDto);
    }

    [HttpPost]
    public async Task<ActionResult<UserManagementDto>> CreateUser(CreateUserDto createUserDto)
    {
        // Check if email already exists
        var existingEmailUsers = await _unitOfWork.Users.FindAsync(u => u.Email == createUserDto.Email);
        if (existingEmailUsers.Any())
        {
            return BadRequest("Email đã được sử dụng bởi người dùng khác.");
        }

        // Check if username already exists
        var existingUsernameUsers = await _unitOfWork.Users.FindAsync(u => u.Username == createUserDto.Username);
        if (existingUsernameUsers.Any())
        {
            return BadRequest("Tên đăng nhập đã được sử dụng bởi người dùng khác.");
        }

        var currentUserId = GetCurrentUserId();
        var user = new User
        {
            Username = createUserDto.Username,
            Email = createUserDto.Email,
            FirstName = createUserDto.FirstName,
            LastName = createUserDto.LastName,
            PasswordHash = _authService.HashPassword(createUserDto.Password),
            Role = createUserDto.Role,
            IsActive = createUserDto.IsActive,
            EmailVerified = true, // Admin tạo user thì tự động verify
            EmailVerificationMethod = EmailVerificationMethod.AdminForce,
            EmailVerifiedAt = DateTime.UtcNow,
            EmailVerifiedById = currentUserId
        };

        await _unitOfWork.Users.AddAsync(user);
        await _unitOfWork.SaveChangesAsync();

        // Không cần gửi email xác thực vì admin đã tạo và tự động verify

        // Lấy thông tin admin để hiển thị
        var admin = await _unitOfWork.Users.GetByIdAsync(currentUserId);
        
        var userDto = new UserManagementDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            Role = user.Role,
            RoleName = GetRoleName(user.Role),
            IsActive = user.IsActive,
            EmailVerified = user.EmailVerified,
            EmailVerifiedAt = user.EmailVerifiedAt,
            EmailVerificationMethod = user.EmailVerificationMethod,
            EmailVerifiedByName = admin?.FullName,
            LastLoginDate = user.LastLoginDate,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        };

        return CreatedAtAction(nameof(GetUser), new { id = user.Id }, userDto);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateUser(int id, UpdateUserDto updateUserDto)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        // Prevent admin from changing their own status
        var currentUserId = GetCurrentUserId();
        if (currentUserId == id && !updateUserDto.IsActive)
        {
            return BadRequest("Bạn không thể tự khóa tài khoản của mình.");
        }

        // Check if email is being changed and already exists for another user
        if (user.Email != updateUserDto.Email)
        {
            var emailConflict = await _unitOfWork.Users
                .FindAsync(u => u.Email == updateUserDto.Email && u.Id != id);
            if (emailConflict.Any())
            {
                return BadRequest("Email đã được sử dụng bởi người dùng khác.");
            }
        }

        // Check if username is being changed and already exists for another user
        if (user.Username != updateUserDto.Username)
        {
            var usernameConflict = await _unitOfWork.Users
                .FindAsync(u => u.Username == updateUserDto.Username && u.Id != id);
            if (usernameConflict.Any())
            {
                return BadRequest("Tên đăng nhập đã được sử dụng bởi người dùng khác.");
            }
        }

        user.Username = updateUserDto.Username;
        user.Email = updateUserDto.Email;
        user.FirstName = updateUserDto.FirstName;
        user.LastName = updateUserDto.LastName;
        user.Role = updateUserDto.Role;
        user.IsActive = updateUserDto.IsActive;

        await _unitOfWork.Users.UpdateAsync(user);
        await _unitOfWork.SaveChangesAsync();

        return NoContent();
    }

    [HttpPost("{id}/force-verify")]
    public async Task<IActionResult> ForceVerifyEmail(int id)
    {
        var currentUserId = GetCurrentUserId();
        if (!IsCurrentUserAdmin())
        {
            return Forbid();
        }

        var result = await _authService.ForceVerifyEmailAsync(id, currentUserId);
        if (!result.Success)
        {
            return BadRequest(new { message = result.Message });
        }

        return Ok(new { message = result.Message });
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteUser(int id)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        // Prevent admin from deleting their own account
        var currentUserId = GetCurrentUserId();
        if (currentUserId == id)
        {
            return BadRequest("Bạn không thể xóa tài khoản của chính mình.");
        }

        // Check if this is the last admin user
        var adminUsers = await _unitOfWork.Users.FindAsync(u => u.Role == UserRole.Admin && u.IsActive);
        if (user.Role == UserRole.Admin && adminUsers.Count() <= 1)
        {
            return BadRequest("Không thể xóa admin cuối cùng trong hệ thống.");
        }

        await _unitOfWork.Users.DeleteAsync(user);
        await _unitOfWork.SaveChangesAsync();

        return NoContent();
    }

    [HttpPost("{id}/change-password")]
    public async Task<IActionResult> ChangePassword(int id, ChangePasswordDto changePasswordDto)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        var currentUserId = GetCurrentUserId();
        
        // Only allow changing own password or admin changing any password
        if (currentUserId != id && !IsCurrentUserAdmin())
        {
            return Forbid("Bạn chỉ có thể thay đổi mật khẩu của chính mình.");
        }

        // If changing own password, verify current password
        if (currentUserId == id)
        {
            if (!_authService.VerifyPassword(changePasswordDto.CurrentPassword, user.PasswordHash))
            {
                return BadRequest("Mật khẩu hiện tại không đúng.");
            }
        }

        user.PasswordHash = _authService.HashPassword(changePasswordDto.NewPassword);
        await _unitOfWork.Users.UpdateAsync(user);
        await _unitOfWork.SaveChangesAsync();

        return Ok(new { message = "Đổi mật khẩu thành công." });
    }

    [HttpPost("{id}/toggle-status")]
    public async Task<IActionResult> ToggleUserStatus(int id)
    {
        var user = await _unitOfWork.Users.GetByIdAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        var currentUserId = GetCurrentUserId();
        if (currentUserId == id)
        {
            return BadRequest("Bạn không thể thay đổi trạng thái tài khoản của chính mình.");
        }

        // Check if this is the last active admin user
        if (user.Role == UserRole.Admin && user.IsActive)
        {
            var activeAdmins = await _unitOfWork.Users.FindAsync(u => u.Role == UserRole.Admin && u.IsActive);
            if (activeAdmins.Count() <= 1)
            {
                return BadRequest("Không thể khóa admin cuối cùng trong hệ thống.");
            }
        }

        user.IsActive = !user.IsActive;
        await _unitOfWork.Users.UpdateAsync(user);
        await _unitOfWork.SaveChangesAsync();

        var status = user.IsActive ? "kích hoạt" : "khóa";
        return Ok(new { message = $"Đã {status} tài khoản thành công." });
    }

    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<UserManagementDto>>> SearchUsers(
        [FromQuery] string? username,
        [FromQuery] string? email,
        [FromQuery] string? firstName,
        [FromQuery] string? lastName,
        [FromQuery] UserRole? role,
        [FromQuery] bool? isActive)
    {
        var users = await _unitOfWork.Users.GetAllAsync();

        var filteredUsers = users.AsEnumerable();

        if (!string.IsNullOrEmpty(username))
        {
            filteredUsers = filteredUsers.Where(u => 
                u.Username.Contains(username, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(email))
        {
            filteredUsers = filteredUsers.Where(u => 
                u.Email.Contains(email, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(firstName))
        {
            filteredUsers = filteredUsers.Where(u => 
                u.FirstName.Contains(firstName, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(lastName))
        {
            filteredUsers = filteredUsers.Where(u => 
                u.LastName.Contains(lastName, StringComparison.OrdinalIgnoreCase));
        }

        if (role.HasValue)
        {
            filteredUsers = filteredUsers.Where(u => u.Role == role.Value);
        }

        if (isActive.HasValue)
        {
            filteredUsers = filteredUsers.Where(u => u.IsActive == isActive.Value);
        }

        var userDtos = filteredUsers.Select(user => new UserManagementDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            Role = user.Role,
            RoleName = GetRoleName(user.Role),
            IsActive = user.IsActive,
            EmailVerified = user.EmailVerified,
            LastLoginDate = user.LastLoginDate,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt
        }).OrderBy(u => u.Role).ThenBy(u => u.LastName).ToList();

        return Ok(userDtos);
    }

    private static string GetRoleName(UserRole role)
    {
        return role switch
        {
            UserRole.Admin => "Quản trị viên",
            UserRole.Librarian => "Thủ thư",
            UserRole.Assistant => "Trợ lý",
            _ => "Không xác định"
        };
    }

    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
        return userIdClaim != null ? int.Parse(userIdClaim.Value) : 0;
    }

    private bool IsCurrentUserAdmin()
    {
        var roleClaim = User.FindFirst(System.Security.Claims.ClaimTypes.Role);
        return roleClaim?.Value == "Admin";
    }
} 
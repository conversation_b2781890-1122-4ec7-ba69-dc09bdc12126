-- Library Management Sample Data (<PERSON> thứ tự mới nhất)
-- <PERSON><PERSON><PERSON> để thêm dữ liệu mẫu cho ứng dụng quản lý thư viện

USE LibraryManagementDb;
GO

-- <PERSON><PERSON><PERSON> dữ liệu cũ (theo thứ tự để tránh FK constraint)
DELETE FROM BorrowRecords;
DELETE FROM Books;
DELETE FROM Bookshelves;
DELETE FROM Members;
DELETE FROM Users;
DELETE FROM Categories;
DELETE FROM Zones;

-- Reset Identity seeds
DBCC CHECKIDENT ('BorrowRecords', RESEED, 0);
DBCC CHECKIDENT ('Books', RESEED, 0);
DBCC CHECKIDENT ('Members', RESEED, 0);
DBCC CHECKIDENT ('Users', RESEED, 0);
DBCC CHECKIDENT ('Categories', RESEED, 0);
DBCC CHECKIDENT ('Zones', RESEED, 0);
DBCC CHECKIDENT ('Bookshelves', RESEED, 0);

PRINT 'Deleted all existing data and reset identity seeds.';

-- Thêm Zones (theo thứ tự mới nhất)
INSERT INTO Zones (Name, Description, CreatedAt) VALUES
(N'Tầng 3 - Khu Khoa học', N'Khu vực sách khoa học và kỹ thuật', DATEADD(day, 0, GETUTCDATE())),
(N'Tầng 2 - Khu Văn học', N'Khu vực dành cho sách văn học', DATEADD(day, -1, GETUTCDATE())),
(N'Tầng 2 - Khu IT', N'Khu vực chuyên về sách công nghệ thông tin', DATEADD(day, -2, GETUTCDATE())),
(N'Tầng 1 - Khu B', N'Khu vực phía sau tầng 1, yên tĩnh', DATEADD(day, -3, GETUTCDATE())),
(N'Tầng 1 - Khu A', N'Khu vực chính tầng 1, gần lối vào', DATEADD(day, -4, GETUTCDATE()));

-- Thêm Categories (theo thứ tự mới nhất)
INSERT INTO Categories (Name, Description, CreatedAt) VALUES
(N'Lịch sử', N'Sách về lịch sử Việt Nam và thế giới', DATEADD(day, 0, GETUTCDATE())),
(N'Kinh doanh', N'Sách về quản lý, kinh doanh và tài chính', DATEADD(day, -1, GETUTCDATE())),
(N'Khoa học', N'Sách về khoa học tự nhiên và ứng dụng', DATEADD(day, -2, GETUTCDATE())),
(N'Văn học nước ngoài', N'Tác phẩm văn học được dịch từ nước ngoài', DATEADD(day, -3, GETUTCDATE())),
(N'Văn học Việt Nam', N'Tác phẩm văn học của các tác giả Việt Nam', DATEADD(day, -4, GETUTCDATE())),
(N'Công nghệ thông tin', N'Sách về lập trình, phần mềm và công nghệ', DATEADD(day, -5, GETUTCDATE()));

-- Thêm Bookshelves (theo thứ tự mới nhất)
INSERT INTO Bookshelves (Name, Description, ZoneId, Capacity, Status, CreatedAt) VALUES
-- Kệ Lịch sử (Zone 2)
(N'Kệ LS-02', N'Kệ lịch sử thế giới', 4, 100, 'Active', DATEADD(day, 0, GETUTCDATE())),
(N'Kệ LS-01', N'Kệ lịch sử Việt Nam', 4, 100, 'Active', DATEADD(day, -1, GETUTCDATE())),

-- Kệ Kinh doanh (Zone 1)
(N'Kệ KD-02', N'Kệ phát triển bản thân', 5, 100, 'Active', DATEADD(day, -2, GETUTCDATE())),
(N'Kệ KD-01', N'Kệ quản lý kinh doanh', 5, 100, 'Active', DATEADD(day, -3, GETUTCDATE())),

-- Kệ Khoa học (Zone 5)
(N'Kệ KH-02', N'Kệ vật lý thiên văn', 1, 100, 'Active', DATEADD(day, -4, GETUTCDATE())),
(N'Kệ KH-01', N'Kệ khoa học tự nhiên', 1, 100, 'Active', DATEADD(day, -5, GETUTCDATE())),

-- Kệ Văn học nước ngoài (Zone 4)
(N'Kệ VH-04', N'Kệ văn học châu Âu', 2, 100, 'Active', DATEADD(day, -6, GETUTCDATE())),
(N'Kệ VH-03', N'Kệ văn học Anh Mỹ', 2, 100, 'Active', DATEADD(day, -7, GETUTCDATE())),

-- Kệ Văn học Việt Nam (Zone 4)
(N'Kệ VH-02', N'Kệ văn học hiện đại Việt Nam', 2, 100, 'Active', DATEADD(day, -8, GETUTCDATE())),
(N'Kệ VH-01', N'Kệ văn học cổ điển Việt Nam', 2, 100, 'Active', DATEADD(day, -9, GETUTCDATE())),

-- Kệ IT (Zone 3)
(N'Kệ IT-02', N'Kệ sách thiết kế phần mềm', 3, 100, 'Active', DATEADD(day, -10, GETUTCDATE())),
(N'Kệ IT-01', N'Kệ sách lập trình cơ bản', 3, 100, 'Active', DATEADD(day, -11, GETUTCDATE()));

-- Thêm Users (5 tài khoản theo thứ tự mới nhất)
INSERT INTO Users (Username, Email, PasswordHash, FirstName, LastName, Role, IsActive, EmailVerified, CreatedAt) VALUES
('assistant2', '<EMAIL>', '$2b$10$dummyHashForAssistant234567', N'Trợ lý', N'Hai', 3, 1, 1, DATEADD(day, 0, GETUTCDATE())),
('assistant1', '<EMAIL>', '$2b$10$dummyHashForAssistant123456', N'Trợ lý', N'Một', 3, 1, 1, DATEADD(day, -1, GETUTCDATE())),
('librarian2', '<EMAIL>', '$2b$10$dummyHashForLibrarian234567', N'Thư viện', N'Hai', 2, 1, 1, DATEADD(day, -2, GETUTCDATE())),
('librarian1', '<EMAIL>', '$2b$10$dummyHashForLibrarian123456', N'Thư viện', N'Một', 2, 1, 1, DATEADD(day, -3, GETUTCDATE())),
('admin', '<EMAIL>', '$2b$10$dummyHashForAdmin123456789', N'Admin', N'System', 1, 1, 1, DATEADD(day, -4, GETUTCDATE()));

-- Thêm Members (10 thành viên theo thứ tự mới nhất)
INSERT INTO Members (FirstName, LastName, Email, Phone, Address, MembershipDate, Status, CreatedAt) VALUES
(N'Lý Thị', N'Kim', '<EMAIL>', '0900123456', N'741 Đường ABC, Quận 10, TP.HCM', DATEADD(month, -5, GETUTCDATE()), 1, DATEADD(day, 0, GETUTCDATE())),
(N'Ngô Văn', N'Ích', '<EMAIL>', '0909012345', N'369 Đường YZ, Quận 9, TP.HCM', DATEADD(month, -7, GETUTCDATE()), 1, DATEADD(day, -1, GETUTCDATE())),
(N'Bùi Thị', N'Hoa', '<EMAIL>', '0908901234', N'258 Đường VWX, Quận 8, TP.HCM', DATEADD(month, -3, GETUTCDATE()), 1, DATEADD(day, -2, GETUTCDATE())),
(N'Đỗ Văn', N'Giang', '<EMAIL>', '0907890123', N'147 Đường STU, Quận 7, TP.HCM', DATEADD(month, -12, GETUTCDATE()), 1, DATEADD(day, -3, GETUTCDATE())),
(N'Võ Thị', N'Phương', '<EMAIL>', '0906789012', N'987 Đường PQR, Quận 6, TP.HCM', DATEADD(month, -1, GETUTCDATE()), 1, DATEADD(day, -4, GETUTCDATE())),
(N'Hoàng Văn', N'Em', '<EMAIL>', '0905678901', N'654 Đường MNO, Quận 5, TP.HCM', DATEADD(month, -10, GETUTCDATE()), 1, DATEADD(day, -5, GETUTCDATE())),
(N'Phạm Thị', N'Dung', '<EMAIL>', '0904567890', N'321 Đường JKL, Quận 4, TP.HCM', DATEADD(month, -2, GETUTCDATE()), 1, DATEADD(day, -6, GETUTCDATE())),
(N'Lê Văn', N'Cường', '<EMAIL>', '0903456789', N'789 Đường GHI, Quận 3, TP.HCM', DATEADD(month, -8, GETUTCDATE()), 1, DATEADD(day, -7, GETUTCDATE())),
(N'Trần Thị', N'Bình', '<EMAIL>', '0902345678', N'456 Đường DEF, Quận 2, TP.HCM', DATEADD(month, -4, GETUTCDATE()), 1, DATEADD(day, -8, GETUTCDATE())),
(N'Nguyễn Văn', N'An', '<EMAIL>', '0901234567', N'123 Đường ABC, Quận 1, TP.HCM', DATEADD(month, -6, GETUTCDATE()), 1, DATEADD(day, -9, GETUTCDATE()));

-- Thêm Books (22 cuốn sách theo thứ tự mới nhất)
INSERT INTO Books (Title, Author, ISBN, Publisher, PublishedDate, CategoryId, StockQuantity, OnShelfQuantity, BorrowedQuantity, Price, ImageUrl, Description, CreatedAt) VALUES
-- History (CategoryId = 1)
(N'The Guns of August', N'Barbara Tuchman', N'9780345476098', N'Ballantine Books', DATEFROMPARTS(1962, 1, 1), 1, 2, 1, 0, 210000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Guns+August', N'Tháng đầu tiên của Thế chiến thứ nhất', DATEADD(day, 0, GETUTCDATE())),
(N'Sapiens: A Brief History of Humankind', N'Yuval Noah Harari', N'9780062316097', N'Harper', DATEFROMPARTS(2014, 1, 1), 1, 2, 2, 0, 280000, N'https://m.media-amazon.com/images/I/713jIoMO3UL.jpg', N'Lịch sử loài người từ thời tiền sử đến hiện đại', DATEADD(day, -1, GETUTCDATE())),
(N'Lịch sử Việt Nam', N'Trần Trọng Kim', N'9786041080259', N'NXB Tổng hợp TP.HCM', DATEFROMPARTS(1920, 1, 1), 1, 3, 2, 0, 145000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Lich+Su+VN', N'Lịch sử Việt Nam từ thời cổ đại đến cận đại', DATEADD(day, -2, GETUTCDATE())),

-- Business (CategoryId = 2)
(N'The 7 Habits of Highly Effective People', N'Stephen R. Covey', N'9781982137274', N'Simon & Schuster', DATEFROMPARTS(1989, 1, 1), 2, 4, 2, 0, 165000, N'https://images.placeholder.com/150x200/cccccc/969696?text=7+Habits', N'Phương pháp phát triển bản thân và lãnh đạo hiệu quả', DATEADD(day, -3, GETUTCDATE())),
(N'Good to Great', N'Jim Collins', N'9780066620992', N'HarperBusiness', DATEFROMPARTS(2001, 1, 1), 2, 2, 2, 0, 195000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Good+Great', N'Nghiên cứu về các công ty chuyển từ tốt thành xuất sắc', DATEADD(day, -4, GETUTCDATE())),
(N'Think and Grow Rich', N'Napoleon Hill', N'9781585424337', N'TarcherPerigee', DATEFROMPARTS(1937, 1, 1), 2, 3, 2, 0, 175000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Think+Grow+Rich', N'Bí quyết thành công trong kinh doanh và cuộc sống', DATEADD(day, -5, GETUTCDATE())),

-- Science (CategoryId = 3)
(N'Cosmos', N'Carl Sagan', N'9780345331359', N'Ballantine Books', DATEFROMPARTS(1980, 1, 1), 3, 2, 2, 0, 240000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Cosmos', N'Hành trình khám phá vũ trụ qua khoa học', DATEADD(day, -6, GETUTCDATE())),
(N'The Origin of Species', N'Charles Darwin', N'9780451529060', N'Signet Classic', DATEFROMPARTS(1859, 1, 1), 3, 2, 1, 0, 185000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Origin+Species', N'Lý thuyết tiến hóa cách mạng của Darwin', DATEADD(day, -7, GETUTCDATE())),
(N'A Brief History of Time', N'Stephen Hawking', N'9780553380163', N'Bantam', DATEFROMPARTS(1988, 1, 1), 3, 2, 2, 0, 220000, N'https://m.media-amazon.com/images/I/A1yl8SAXOgL.jpg', N'Khám phá vũ trụ và thời gian từ góc nhìn vật lý học', DATEADD(day, -8, GETUTCDATE())),

-- Foreign Literature (CategoryId = 4)
(N'Pride and Prejudice', N'Jane Austen', N'9780141439518', N'Penguin Classics', DATEFROMPARTS(1813, 1, 1), 4, 3, 2, 0, 155000, N'https://m.media-amazon.com/images/I/71Q1tPupKjL.jpg', N'Câu chuyện tình yêu kinh điển của văn học Anh', DATEADD(day, -9, GETUTCDATE())),
(N'The Great Gatsby', N'F. Scott Fitzgerald', N'9780743273565', N'Scribner', DATEFROMPARTS(1925, 1, 1), 4, 2, 2, 0, 165000, N'https://m.media-amazon.com/images/I/81QuEGw8VPL.jpg', N'Tiểu thuyết về giấc mơ Mỹ trong thời đại Jazz', DATEADD(day, -10, GETUTCDATE())),
(N'To Kill a Mockingbird', N'Harper Lee', N'9780061120084', N'Harper Perennial', DATEFROMPARTS(1960, 1, 1), 4, 3, 2, 0, 195000, N'https://m.media-amazon.com/images/I/71FxgtFKcqL.jpg', N'Câu chuyện về công lý và thiên kiến ở miền Nam nước Mỹ', DATEADD(day, -11, GETUTCDATE())),
(N'1984', N'George Orwell', N'9780451524935', N'Signet Classic', DATEFROMPARTS(1949, 1, 1), 4, 4, 2, 0, 180000, N'https://m.media-amazon.com/images/I/71kxa1-0mfL.jpg', N'Tiểu thuyết dystopia kinh điển về chủ nghĩa toàn trị', DATEADD(day, -12, GETUTCDATE())),

-- Vietnamese Literature (CategoryId = 5)
(N'Lão Hạc', N'Nam Cao', N'9786041001254', N'NXB Văn học', DATEFROMPARTS(1943, 1, 1), 5, 3, 3, 0, 75000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Lao+Hac', N'Truyện ngắn nổi tiếng về số phận con người', DATEADD(day, -13, GETUTCDATE())),
(N'Dế Mèn phiêu lưu ký', N'Tô Hoài', N'9786041000547', N'NXB Kim Đồng', DATEFROMPARTS(1941, 1, 1), 5, 4, 3, 0, 85000, N'https://images.placeholder.com/150x200/cccccc/969696?text=De+Men', N'Truyện thiếu nhi kinh điển Việt Nam', DATEADD(day, -14, GETUTCDATE())),
(N'Số đỏ', N'Vũ Trọng Phụng', N'9786041033890', N'NXB Văn học', DATEFROMPARTS(1936, 1, 1), 5, 4, 4, 0, 95000, N'https://images.placeholder.com/150x200/cccccc/969696?text=So+Do', N'Tiểu thuyết hiện thực phê phán nổi tiếng', DATEADD(day, -15, GETUTCDATE())),
(N'Truyện Kiều', N'Nguyễn Du', N'9786041141179', N'NXB Kim Đồng', DATEFROMPARTS(1820, 1, 1), 5, 6, 4, 0, 120000, N'https://images.placeholder.com/150x200/cccccc/969696?text=Truyen+Kieu', N'Tác phẩm kinh điển của văn học Việt Nam', DATEADD(day, -16, GETUTCDATE())),

-- IT Books (CategoryId = 6)
(N'The Pragmatic Programmer', N'Andrew Hunt, David Thomas', N'9780135957059', N'Addison-Wesley', DATEFROMPARTS(2019, 1, 1), 6, 2, 3, 0, 890000, N'https://m.media-amazon.com/images/I/41as+WafrFL._SX378_BO1,204,203,200_.jpg', N'Hướng dẫn thực tế cho lập trình viên chuyên nghiệp', DATEADD(day, -17, GETUTCDATE())),
(N'C# in Depth', N'Jon Skeet', N'9781617294532', N'Manning Publications', DATEFROMPARTS(2019, 1, 1), 6, 2, 2, 0, 780000, N'https://m.media-amazon.com/images/I/41JYiuLn0QL._SX397_BO1,204,203,200_.jpg', N'Hiểu sâu về ngôn ngữ lập trình C#', DATEADD(day, -18, GETUTCDATE())),
(N'JavaScript: The Good Parts', N'Douglas Crockford', N'9780596517748', N'O''Reilly Media', DATEFROMPARTS(2008, 1, 1), 6, 3, 3, 0, 650000, N'https://m.media-amazon.com/images/I/5166ztxN-eL._SX381_BO1,204,203,200_.jpg', N'Những phần tốt nhất của ngôn ngữ JavaScript', DATEADD(day, -19, GETUTCDATE())),
(N'Design Patterns: Elements of Reusable Object-Oriented Software', N'Erich Gamma, Richard Helm', N'9780201633610', N'Addison-Wesley', DATEFROMPARTS(1994, 1, 1), 6, 1, 3, 0, 920000, N'https://m.media-amazon.com/images/I/81gtKoapHFL.jpg', N'Các mẫu thiết kế phần mềm hướng đối tượng kinh điển', DATEADD(day, -20, GETUTCDATE())),
(N'Clean Code: A Handbook of Agile Software Craftsmanship', N'Robert C. Martin', N'9780132350884', N'Prentice Hall', DATEFROMPARTS(2008, 1, 1), 6, 2, 3, 0, 850000, N'https://m.media-amazon.com/images/I/41xShlnTZTL._SX376_BO1,204,203,200_.jpg', N'Hướng dẫn viết code sạch và dễ bảo trì cho các lập trình viên', DATEADD(day, -21, GETUTCDATE()));

-- Thêm Borrow Records (12 bản ghi theo thứ tự mới nhất)
INSERT INTO BorrowRecords (BookId, MemberId, BorrowDate, DueDate, ReturnDate, Status, Notes, Quantity, IsPartialReturn, ReturnedQuantity, CreatedAt) VALUES
-- Sách đang được mượn (Status = 1) - 6 bản ghi
(5, 5, DATEADD(day, -1, GETUTCDATE()), DATEADD(day, 13, GETUTCDATE()), NULL, 1, N'Mượn sách mới nhất', 1, 0, 0, DATEADD(day, -1, GETUTCDATE())),
(4, 4, DATEADD(day, -2, GETUTCDATE()), DATEADD(day, 12, GETUTCDATE()), NULL, 1, NULL, 1, 0, 0, DATEADD(day, -2, GETUTCDATE())),
(2, 2, DATEADD(day, -3, GETUTCDATE()), DATEADD(day, 11, GETUTCDATE()), NULL, 1, NULL, 1, 0, 0, DATEADD(day, -3, GETUTCDATE())),
(6, 6, DATEADD(day, -4, GETUTCDATE()), DATEADD(day, 10, GETUTCDATE()), NULL, 1, NULL, 1, 0, 0, DATEADD(day, -4, GETUTCDATE())),
(1, 1, DATEADD(day, -5, GETUTCDATE()), DATEADD(day, 9, GETUTCDATE()), NULL, 1, NULL, 1, 0, 0, DATEADD(day, -5, GETUTCDATE())),
(3, 3, DATEADD(day, -7, GETUTCDATE()), DATEADD(day, 7, GETUTCDATE()), NULL, 1, NULL, 1, 0, 0, DATEADD(day, -7, GETUTCDATE())),

-- Sách quá hạn (Status = 3) - 3 bản ghi
(8, 8, DATEADD(day, -18, GETUTCDATE()), DATEADD(day, -4, GETUTCDATE()), NULL, 3, N'Quá hạn 4 ngày', 1, 0, 0, DATEADD(day, -18, GETUTCDATE())),
(7, 7, DATEADD(day, -20, GETUTCDATE()), DATEADD(day, -6, GETUTCDATE()), NULL, 3, N'Quá hạn 6 ngày', 1, 0, 0, DATEADD(day, -20, GETUTCDATE())),
(9, 9, DATEADD(day, -25, GETUTCDATE()), DATEADD(day, -11, GETUTCDATE()), NULL, 3, N'Quá hạn 11 ngày', 1, 0, 0, DATEADD(day, -25, GETUTCDATE())),

-- Sách đã trả (Status = 2) - 3 bản ghi
(10, 10, DATEADD(day, -25, GETUTCDATE()), DATEADD(day, -11, GETUTCDATE()), DATEADD(day, -12, GETUTCDATE()), 2, N'Trả đúng hạn', 1, 0, 1, DATEADD(day, -25, GETUTCDATE())),
(11, 1, DATEADD(day, -30, GETUTCDATE()), DATEADD(day, -16, GETUTCDATE()), DATEADD(day, -17, GETUTCDATE()), 2, NULL, 1, 0, 1, DATEADD(day, -30, GETUTCDATE())),
(12, 2, DATEADD(day, -35, GETUTCDATE()), DATEADD(day, -21, GETUTCDATE()), DATEADD(day, -22, GETUTCDATE()), 2, NULL, 1, 0, 1, DATEADD(day, -35, GETUTCDATE()));

-- Cập nhật BorrowedQuantity dựa trên số lượng mượn
UPDATE Books SET 
    BorrowedQuantity = 
    (SELECT COUNT(1) FROM BorrowRecords WHERE BookId = Books.Id AND (Status = 1 OR Status = 3))
WHERE Id IN (
    SELECT DISTINCT BookId FROM BorrowRecords WHERE Status = 1 OR Status = 3
);

PRINT 'Sample data inserted successfully!';
PRINT 'Login accounts:';
PRINT '- Admin: <EMAIL> / Admin123!';
PRINT '- Librarian 1: <EMAIL> / Librarian123!';
PRINT '- Librarian 2: <EMAIL> / Librarian123!';
PRINT '- Assistant 1: <EMAIL> / Assistant123!';
PRINT '- Assistant 2: <EMAIL> / Assistant123!'; 
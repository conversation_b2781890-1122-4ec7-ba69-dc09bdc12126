import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Observable } from "rxjs";
import {
  Book,
  CreateBook,
  UpdateBook,
  BookOnShelf,
} from "../models/book.model";
import { environment } from "../../environments/environment";

@Injectable({
  providedIn: "root",
})
export class BookService {
  private apiUrl = `${environment.apiUrl}/books`;

  constructor(private http: HttpClient) {}

  getBooks(): Observable<Book[]> {
    return this.http.get<Book[]>(this.apiUrl);
  }

  getBook(id: number): Observable<Book> {
    return this.http.get<Book>(`${this.apiUrl}/${id}`);
  }

  createBook(book: CreateBook): Observable<Book> {
    return this.http.post<Book>(this.apiUrl, book);
  }

  updateBook(id: number, book: UpdateBook): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, book);
  }

  deleteBook(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  searchBooks(
    title?: string,
    author?: string,
    categoryId?: number
  ): Observable<Book[]> {
    let params = new HttpParams();

    if (title) {
      params = params.set("title", title);
    }
    if (author) {
      params = params.set("author", author);
    }
    if (categoryId) {
      params = params.set("categoryId", categoryId.toString());
    }

    return this.http.get<Book[]>(`${this.apiUrl}/search`, { params });
  }

  // Tìm kiếm tổng hợp (tìm trong title, author, ISBN cùng lúc)
  searchBooksGeneral(searchTerm: string): Observable<Book[]> {
    let params = new HttpParams();
    params = params.set("q", searchTerm);
    
    return this.http.get<Book[]>(`${this.apiUrl}/search-general`, { params });
  }

  // Xóa nhiều sách
  deleteMultipleBooks(bookIds: number[]): Observable<any> {
    return this.http.post(`${this.apiUrl}/delete-multiple`, bookIds);
  }

  uploadImage(formData: FormData): Observable<{ imageUrl: string }> {
    return this.http.post<{ imageUrl: string }>(
      `${environment.apiUrl}/upload/image`,
      formData
    );
  }

  getBooksInStorage(): Observable<Book[]> {
    return this.http.get<Book[]>(`${this.apiUrl}/in-storage`);
  }

  getBooksOnShelf(): Observable<BookOnShelf[]> {
    return this.http.get<BookOnShelf[]>(`${this.apiUrl}/on-shelf`);
  }
}

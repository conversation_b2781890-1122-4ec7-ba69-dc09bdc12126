import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { HttpClientModule } from '@angular/common/http';
import { ZoneService } from 'src/app/services/zone.service';
import { Zone } from 'src/app/services/zone.service';

@Component({
  selector: 'app-zone-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatTableModule,
    MatIconModule,
    HttpClientModule
  ],
  template: `
    <div class="zone-list-container">
      <mat-card class="zone-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>map</mat-icon>
            Quản lý Khu vực
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <form (ngSubmit)="submitZone()" class="zone-form">
            <mat-form-field appearance="outline" class="form-field">
              <mat-label>Tên khu vực</mat-label>
              <input matInput [(ngModel)]="form.name" name="name" required />
            </mat-form-field>

            <mat-form-field appearance="outline" class="form-field description-field">
              <mat-label>Mô tả</mat-label>
              <input matInput [(ngModel)]="form.description" name="description" />
            </mat-form-field>

            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit">
                {{ isEditMode ? 'Cập nhật' : 'Thêm khu vực' }}
              </button>
              <button mat-stroked-button type="button" (click)="cancelEdit()" *ngIf="isEditMode">
                Hủy
              </button>
            </div>
          </form>

          <div class="table-container">
            <table mat-table [dataSource]="zones" class="mat-elevation-z1 zones-table">
              <ng-container matColumnDef="stt">
                <th mat-header-cell *matHeaderCellDef>STT</th>
                <td mat-cell *matCellDef="let z; let i = index">{{ i + 1 }}</td>
              </ng-container>
              
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Tên khu vực</th>
                <td mat-cell *matCellDef="let z">{{ z.name }}</td>
              </ng-container>

              <ng-container matColumnDef="description">
                <th mat-header-cell *matHeaderCellDef>Mô tả</th>
                <td mat-cell *matCellDef="let z">{{ z.description }}</td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Thao tác</th>
                <td mat-cell *matCellDef="let z">
                  <button mat-icon-button color="primary" (click)="editZone(z)" matTooltip="Chỉnh sửa">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="deleteZone(z)" matTooltip="Xóa">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .zone-list-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .zone-card {
      border-radius: var(--radius-large);
      box-shadow: var(--shadow-md);
      overflow: hidden;
    }

    mat-card-header {
      padding: var(--spacing-md) var(--spacing-lg);
      border-bottom: 1px solid var(--color-outline);
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      margin: 0;
      font-size: var(--font-size-xl);
    }

    mat-card-title mat-icon {
      color: var(--color-primary);
    }

    mat-card-content {
      padding: var(--spacing-lg);
    }

    .zone-form {
      display: flex;
      gap: var(--spacing-md);
      flex-wrap: wrap;
      margin-bottom: var(--spacing-xl);
      align-items: flex-start;
      padding: var(--spacing-md);
      background-color: var(--color-surface-variant);
      border-radius: var(--radius-medium);
    }

    .form-field {
      min-width: 220px;
      flex: 1;
    }

    .description-field {
      min-width: 320px;
      flex: 2;
    }

    .form-actions {
      display: flex;
      gap: var(--spacing-sm);
      align-self: flex-end;
    }

    .table-container {
      margin-top: var(--spacing-lg);
      overflow-x: auto;
    }

    .zones-table {
      width: 100%;
    }

    @media (max-width: 768px) {
      .zone-form {
        flex-direction: column;
      }

      .form-actions {
        align-self: flex-start;
        margin-top: var(--spacing-sm);
      }
    }
  `]
})
export class ZoneListComponent implements OnInit {
  zones: Zone[] = [];
  isEditMode = false;
  editingZoneId: number | null = null;

  form: Partial<Zone> = {
    name: '',
    description: ''
  };

  displayedColumns = ['stt', 'name', 'description', 'actions'];

  constructor(private zoneService: ZoneService) {}

  ngOnInit(): void {
    this.loadZones();
  }

  loadZones(): void {
    this.zoneService.getAllZones().subscribe({
      next: (data) => (this.zones = data),
      error: (err) => console.error('Lỗi tải khu vực:', err)
    });
  }

  submitZone() {
    if (!this.form.name?.trim()) {
      alert('Tên khu vực là bắt buộc');
      return;
    }

    if (this.isEditMode && this.editingZoneId !== null) {
      this.zoneService.updateZone(this.editingZoneId, {
        id: this.editingZoneId,
        ...this.form
      } as Zone).subscribe({
        next: () => {
          this.loadZones();
          this.cancelEdit();
        },
        error: (err) => console.error('Lỗi cập nhật khu vực:', err)
      });
    } else {
      this.zoneService.createZone({
        id: 0,
        ...this.form
      } as Zone).subscribe({
        next: () => {
          this.loadZones();
          this.resetForm();
        },
        error: (err) => console.error('Lỗi thêm khu vực:', err)
      });
    }
  }

  editZone(zone: Zone) {
    this.form = { name: zone.name, description: zone.description };
    this.editingZoneId = zone.id;
    this.isEditMode = true;
  }

  deleteZone(zone: Zone) {
    const confirmed = confirm(`Bạn chắc chắn muốn xóa khu vực "${zone.name}"?`);
    if (confirmed) {
      this.zoneService.deleteZone(zone.id).subscribe({
        next: () => this.loadZones(),
        error: (err) => console.error('Lỗi xóa khu vực:', err)
      });
    }
  }

  cancelEdit() {
    this.resetForm();
    this.isEditMode = false;
    this.editingZoneId = null;
  }

  resetForm() {
    this.form = { name: '', description: '' };
  }
}

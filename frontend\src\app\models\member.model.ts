export interface Member {
  id: number;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  phone?: string;
  address?: string;
  membershipDate: string;
  status: MemberStatus;
  statusName: string;
  notes?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface CreateMember {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address?: string;
  notes?: string;
}

export interface UpdateMember {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address?: string;
  status: MemberStatus;
  notes?: string;
}

export enum MemberStatus {
  Active = 1,
  Suspended = 2,
  Expired = 3,
  Banned = 4
}

export interface MemberStatusOption {
  value: MemberStatus;
  label: string;
}

export const MEMBER_STATUS_OPTIONS: MemberStatusOption[] = [
  { value: MemberStatus.Active, label: 'Hoạt động' },
  { value: MemberStatus.Banned, label: 'Bị cấm' }
];

// For borrow history
export interface BorrowRecord {
  id: number;
  bookId: number;
  bookTitle: string;
  bookAuthor: string;
  memberId: number;
  memberName: string;
  borrowDate: string;
  dueDate: string;
  returnDate?: string;
  status: BorrowStatus;
  statusName: string;
  notes?: string;
  fine?: number;
  isOverdue: boolean;
  daysOverdue: number;
  createdAt: string;
}

export enum BorrowStatus {
  Borrowed = 1,
  Returned = 2,
  Overdue = 3,
  Lost = 4,
  Renewed = 5
} 
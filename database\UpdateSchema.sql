-- Script để cập nhật database schema
-- <PERSON><PERSON><PERSON>i quyết các vấn đề logic trong quản lý số lượng sách và mượn/trả

USE LibraryManagementDb;
GO

PRINT 'Bắt đầu cập nhật database schema...';

-- 1. <PERSON>h<PERSON><PERSON> các cột mới vào bảng Books nếu chưa tồn tại
-- 1.1. Thêm cột StockQuantity nếu chưa có
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Books') AND name = 'StockQuantity')
BEGIN
    ALTER TABLE Books ADD StockQuantity INT NOT NULL DEFAULT 0;
    
    -- Cập nhật StockQuantity dựa trên Quantity hiện tại
    UPDATE Books SET StockQuantity = Quantity;
    
    PRINT 'Đã thêm cột StockQuantity vào bảng Books';
END

-- 1.2. Th<PERSON><PERSON> cột OnShelfQuantity nếu chưa có
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Books') AND name = 'OnShelfQuantity')
BEGIN
    ALTER TABLE Books ADD OnShelfQuantity INT NOT NULL DEFAULT 0;
    PRINT 'Đã thêm cột OnShelfQuantity vào bảng Books';
END

-- 1.3. Thêm cột BorrowedQuantity vào bảng Books
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Books') AND name = 'BorrowedQuantity')
BEGIN
    ALTER TABLE Books ADD BorrowedQuantity INT NOT NULL DEFAULT 0;
    PRINT 'Đã thêm cột BorrowedQuantity vào bảng Books';
END

-- 2. Thêm các cột mới vào bảng BorrowRecords
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('BorrowRecords') AND name = 'Quantity')
BEGIN
    ALTER TABLE BorrowRecords ADD Quantity INT NOT NULL DEFAULT 1;
    PRINT 'Đã thêm cột Quantity vào bảng BorrowRecords';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('BorrowRecords') AND name = 'ReturnedQuantity')
BEGIN
    ALTER TABLE BorrowRecords ADD ReturnedQuantity INT NOT NULL DEFAULT 0;
    PRINT 'Đã thêm cột ReturnedQuantity vào bảng BorrowRecords';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('BorrowRecords') AND name = 'IsPartialReturn')
BEGIN
    ALTER TABLE BorrowRecords ADD IsPartialReturn BIT NOT NULL DEFAULT 0;
    PRINT 'Đã thêm cột IsPartialReturn vào bảng BorrowRecords';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('BorrowRecords') AND name = 'BookshelfId')
BEGIN
    ALTER TABLE BorrowRecords ADD BookshelfId INT NULL;
    PRINT 'Đã thêm cột BookshelfId vào bảng BorrowRecords';
    
    -- Thêm foreign key constraint
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_BorrowRecords_Bookshelves')
    BEGIN
        ALTER TABLE BorrowRecords ADD CONSTRAINT FK_BorrowRecords_Bookshelves
        FOREIGN KEY (BookshelfId) REFERENCES Bookshelves(Id)
        ON DELETE SET NULL;
        PRINT 'Đã thêm foreign key constraint cho BookshelfId';
    END
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('BorrowRecords') AND name = 'LocationCode')
BEGIN
    ALTER TABLE BorrowRecords ADD LocationCode NVARCHAR(20) NULL;
    PRINT 'Đã thêm cột LocationCode vào bảng BorrowRecords';
END

-- 3. Cập nhật dữ liệu hiện có
PRINT 'Cập nhật dữ liệu hiện có...';

-- 3.1. Cập nhật BorrowedQuantity trong Books dựa trên các BorrowRecords hiện tại
-- Chỉ cập nhật nếu đã tồn tại tất cả các cột cần thiết
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Books') AND name = 'BorrowedQuantity')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('BorrowRecords') AND name = 'Quantity')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('BorrowRecords') AND name = 'ReturnedQuantity')
BEGIN
    UPDATE Books
    SET BorrowedQuantity = (
        SELECT ISNULL(SUM(BR.Quantity - BR.ReturnedQuantity), 0)
        FROM BorrowRecords BR
        WHERE BR.BookId = Books.Id
        AND (BR.ReturnDate IS NULL OR BR.IsPartialReturn = 1)
    );
    PRINT 'Đã cập nhật BorrowedQuantity cho Books';
END
ELSE
BEGIN
    PRINT 'Không thể cập nhật BorrowedQuantity do thiếu cột cần thiết';
END

-- 3.2. Đảm bảo tính nhất quán trong Books (nếu tất cả các cột đã tồn tại)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Books') AND name = 'StockQuantity')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Books') AND name = 'OnShelfQuantity')
BEGIN
    -- Không cập nhật Quantity vì nó là computed column trong entity
    PRINT 'Đã đảm bảo tính nhất quán giữa các cột số lượng trong Books';
END

-- 3.3. Cập nhật BookshelfId và LocationCode trong BorrowRecords
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('BorrowRecords') AND name = 'BookshelfId')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('BorrowRecords') AND name = 'LocationCode')
BEGIN
    UPDATE BR
    SET BR.BookshelfId = B.BookshelfId,
        BR.LocationCode = B.LocationCode
    FROM BorrowRecords BR
    JOIN Books B ON BR.BookId = B.Id
    WHERE B.BookshelfId IS NOT NULL
    AND BR.ReturnDate IS NULL;
    PRINT 'Đã cập nhật BookshelfId và LocationCode cho BorrowRecords';
END

-- 4. Thêm BorrowStatus.PartiallyReturned nếu chưa có
IF NOT EXISTS (SELECT 1 FROM sys.columns c 
               JOIN sys.types t ON c.system_type_id = t.system_type_id
               WHERE c.object_id = OBJECT_ID('BorrowRecords') 
               AND c.name = 'Status' 
               AND t.name = 'int')
BEGIN
    PRINT 'Không thể thêm giá trị enum mới vào cột Status, hãy cập nhật trong code';
END
ELSE
BEGIN
    PRINT 'Đã cập nhật BorrowStatus enum trong code';
END

PRINT 'Cập nhật database schema hoàn tất!';
GO 
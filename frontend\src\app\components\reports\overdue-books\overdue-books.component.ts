import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule, MatTable } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { ReportService, BorrowRecord } from '../../../services/report.service';

@Component({
  selector: 'app-overdue-books',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressBarModule,
    MatTooltipModule,
    FormsModule,
    RouterModule
  ],
  templateUrl: './overdue-books.component.html',
  styleUrls: ['./overdue-books.component.scss']
})
export class OverdueBooksComponent implements OnInit {
  displayedColumns: string[] = [
    'stt',
    'bookTitle',
    'memberName',
    'borrowDate',
    'dueDate',
    'daysOverdue',
    'fine',
    'actions'
  ];
  dataSource: BorrowRecord[] = [];
  filteredData: BorrowRecord[] = [];
  isLoading = true;
  error = '';
  filterValue = '';

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatTable) table!: MatTable<BorrowRecord>;

  constructor(
    private reportService: ReportService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadOverdueBooks();
  }

  loadOverdueBooks(): void {
    this.isLoading = true;
    this.error = '';

    this.reportService.getOverdueBooks().subscribe({
      next: (data) => {
        this.dataSource = data;
        this.filteredData = data;
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Không thể tải dữ liệu sách quá hạn';
        this.isLoading = false;
        this.snackBar.open(this.error, 'Đóng', { duration: 3000 });
      }
    });
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value.trim().toLowerCase();
    this.filterValue = filterValue;

    if (filterValue) {
      this.filteredData = this.dataSource.filter(record =>
        record.bookTitle.toLowerCase().includes(filterValue) ||
        record.bookAuthor.toLowerCase().includes(filterValue) ||
        record.memberName.toLowerCase().includes(filterValue)
      );
    } else {
      this.filteredData = this.dataSource;
    }

    if (this.table) {
      this.table.renderRows();
    }
  }

  clearFilter(): void {
    this.filterValue = '';
    this.filteredData = this.dataSource;
    if (this.table) {
      this.table.renderRows();
    }
  }

  exportToCsv(): void {
    if (!this.dataSource || this.dataSource.length === 0) {
      this.snackBar.open('Không có dữ liệu để xuất', 'Đóng', { duration: 3000 });
      return;
    }

    // Define CSV headers
    const headers = [
      'ID',
      'Tiêu đề sách',
      'Tác giả',
      'Thành viên',
      'Ngày mượn',
      'Ngày hẹn trả',
      'Số ngày quá hạn',
      'Phí phạt'
    ];

    // Convert data to CSV format
    const csvData = this.filteredData.map(record => [
      record.id,
      record.bookTitle,
      record.bookAuthor,
      record.memberName,
      new Date(record.borrowDate).toLocaleDateString('vi-VN'),
      new Date(record.dueDate).toLocaleDateString('vi-VN'),
      record.daysOverdue,
      record.fine?.toFixed(2) || '0.00'
    ]);

    // Combine headers and data
    const csvContent = [
      headers.join(','),
      ...csvData.map(row => row.join(','))
    ].join('\n');

    // Create a Blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `sach-qua-han-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  printReport(): void {
    if (!this.dataSource || this.dataSource.length === 0) {
      this.snackBar.open('Không có dữ liệu để in', 'Đóng', { duration: 3000 });
      return;
    }

    const printContent = document.createElement('div');
    printContent.innerHTML = `
      <h1 style="text-align: center;">Báo cáo sách quá hạn</h1>
      <p style="text-align: center;">Ngày xuất báo cáo: ${new Date().toLocaleDateString('vi-VN')}</p>
      <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
        <thead>
          <tr>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">ID</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Tiêu đề sách</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Tác giả</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Thành viên</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Ngày mượn</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Ngày hẹn trả</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Số ngày quá hạn</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Phí phạt</th>
          </tr>
        </thead>
        <tbody>
          ${this.filteredData.map(record => `
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">${record.id}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${record.bookTitle}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${record.bookAuthor}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${record.memberName}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${new Date(record.borrowDate).toLocaleDateString('vi-VN')}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${new Date(record.dueDate).toLocaleDateString('vi-VN')}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${record.daysOverdue}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${record.fine?.toFixed(2) || '0.00'}</td>
            </tr>
          `).join('')}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="7" style="border: 1px solid #ddd; padding: 8px; text-align: right;"><strong>Tổng phí phạt:</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${this.filteredData.reduce((sum, record) => sum + (record.fine || 0), 0).toFixed(2)}</td>
          </tr>
        </tfoot>
      </table>
      <p style="margin-top: 20px;">Tổng số sách quá hạn: ${this.filteredData.length}</p>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Báo cáo sách quá hạn</title>
            <style>
              body { font-family: Arial, sans-serif; }
              table { width: 100%; border-collapse: collapse; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              th { background-color: #f2f2f2; }
              h1, p { text-align: center; }
              @media print {
                body { margin: 0; padding: 20px; }
              }
            </style>
          </head>
          <body>
            ${printContent.innerHTML}
          </body>
        </html>
      `);
      printWindow.document.close();
      setTimeout(() => {
        printWindow.print();
      }, 500);
    } else {
      this.snackBar.open('Không thể mở cửa sổ in. Vui lòng kiểm tra cài đặt trình duyệt của bạn.', 'Đóng', { duration: 5000 });
    }
  }

  getTotalFine(): number {
    return this.filteredData.reduce((sum, record) => sum + (record.fine || 0), 0);
  }
}
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using LibraryManagement.Core.Interfaces;
using LibraryManagement.Core.Entities;
using LibraryManagement.Application.DTOs;
using LibraryManagement.Core.Enums;
using System.Linq;

namespace LibraryManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
// [Authorize] // Require authentication for all endpoints
[AllowAnonymous] // Allow anonymous access for all endpoints
public class ReportsController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public ReportsController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    [HttpGet("dashboard")]
    [Authorize(Policy = "BorrowOperations")] // All staff can view dashboard
    public async Task<ActionResult<DashboardStatsDto>> GetDashboardStats()
    {
        // Get counts from repositories
        var books = await _unitOfWork.Books.GetAllAsync();
        var members = await _unitOfWork.Members.GetAllAsync();
        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();
        var categories = await _unitOfWork.Categories.GetAllAsync();
        var users = await _unitOfWork.Users.GetAllAsync();

        // Calculate statistics
        var activeBorrows = borrowRecords.Count(br => br.ReturnDate == null);
        var overdueBooks = borrowRecords.Count(br => br.ReturnDate == null && br.DueDate < DateTime.Now);
        var totalFines = borrowRecords.Where(br => br.Fine.HasValue).Sum(br => br.Fine.Value);

        var stats = new DashboardStatsDto
        {
            TotalBooks = books.Count(),
            TotalMembers = members.Count(),
            TotalBorrows = borrowRecords.Count(),
            ActiveBorrows = activeBorrows,
            OverdueBooks = overdueBooks,
            TotalFines = totalFines,
            TotalCategories = categories.Count(),
            TotalUsers = users.Count()
        };

        return Ok(stats);
    }

    [HttpGet("popular-books")]
    [Authorize(Policy = "BorrowOperations")]
    public async Task<ActionResult<IEnumerable<PopularBookDto>>> GetPopularBooks(int count = 5)
    {
        var books = await _unitOfWork.Books.GetAllAsync();
        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();
        var categories = await _unitOfWork.Categories.GetAllAsync();

        var categoryDict = categories.ToDictionary(c => c.Id, c => c.Name);

        var popularBooks = books
            .Select(book => new
            {
                Book = book,
                BorrowCount = borrowRecords.Count(br => br.BookId == book.Id)
            })
            .OrderByDescending(x => x.BorrowCount)
            .Take(count)
            .Select(x => new PopularBookDto
            {
                BookId = x.Book.Id,
                Title = x.Book.Title,
                Author = x.Book.Author,
                BorrowCount = x.BorrowCount,
                ImageUrl = x.Book.ImageUrl,
                CategoryName = categoryDict.GetValueOrDefault(x.Book.CategoryId, "Unknown")
            })
            .ToList();

        return Ok(popularBooks);
    }

    [HttpGet("active-members")]
    [Authorize(Policy = "BorrowOperations")]
    public async Task<ActionResult<IEnumerable<ActiveMemberDto>>> GetActiveMembers(int count = 5)
    {
        var members = await _unitOfWork.Members.GetAllAsync();
        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();

        var activeMembers = members
            .Select(member => new
            {
                Member = member,
                BorrowCount = borrowRecords.Count(br => br.MemberId == member.Id)
            })
            .OrderByDescending(x => x.BorrowCount)
            .Take(count)
            .Select(x => new ActiveMemberDto
            {
                MemberId = x.Member.Id,
                FullName = x.Member.FullName,
                Email = x.Member.Email,
                BorrowCount = x.BorrowCount,
                Status = x.Member.Status
            })
            .ToList();

        return Ok(activeMembers);
    }

    [HttpGet("category-stats")]
    [Authorize(Policy = "BorrowOperations")]
    public async Task<ActionResult<IEnumerable<CategoryStatsDto>>> GetCategoryStats()
    {
        var categories = await _unitOfWork.Categories.GetAllAsync();
        var books = await _unitOfWork.Books.GetAllAsync();
        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();

        var categoryStats = categories
            .Select(category => new CategoryStatsDto
            {
                CategoryId = category.Id,
                CategoryName = category.Name,
                BookCount = books.Count(b => b.CategoryId == category.Id),
                BorrowCount = borrowRecords.Count(br => 
                    books.FirstOrDefault(b => b.Id == br.BookId)?.CategoryId == category.Id)
            })
            .OrderByDescending(x => x.BorrowCount)
            .ToList();

        return Ok(categoryStats);
    }

    [HttpGet("monthly-stats")]
    [Authorize(Policy = "BorrowOperations")]
    public async Task<ActionResult<IEnumerable<MonthlyStatsDto>>> GetMonthlyStats(int year = 0)
    {
        if (year == 0)
        {
            year = DateTime.Now.Year;
        }

        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();
        var members = await _unitOfWork.Members.GetAllAsync();

        var monthlyStats = Enumerable.Range(1, 12)
            .Select(month => new MonthlyStatsDto
            {
                Year = year,
                Month = month,
                MonthName = new DateTime(year, month, 1).ToString("MMMM"),
                NewBorrows = borrowRecords.Count(br => 
                    br.BorrowDate.Year == year && br.BorrowDate.Month == month),
                Returns = borrowRecords.Count(br => 
                    br.ReturnDate.HasValue && br.ReturnDate.Value.Year == year && br.ReturnDate.Value.Month == month),
                NewMembers = members.Count(m => 
                    m.MembershipDate.Year == year && m.MembershipDate.Month == month),
                TotalFines = borrowRecords
                    .Where(br => br.Fine.HasValue && br.ReturnDate.HasValue && 
                           br.ReturnDate.Value.Year == year && br.ReturnDate.Value.Month == month)
                    .Sum(br => br.Fine.Value)
            })
            .ToList();

        return Ok(monthlyStats);
    }

    [HttpGet("daily-stats")]
    [Authorize(Policy = "BorrowOperations")]
    public async Task<ActionResult<IEnumerable<DailyStatsDto>>> GetDailyStats(DateTime? startDate = null, DateTime? endDate = null)
    {
        if (!startDate.HasValue)
        {
            startDate = DateTime.Now.AddDays(-30);
        }

        if (!endDate.HasValue)
        {
            endDate = DateTime.Now;
        }

        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();

        var dailyStats = Enumerable.Range(0, (endDate.Value - startDate.Value).Days + 1)
            .Select(offset => startDate.Value.AddDays(offset))
            .Select(date => new DailyStatsDto
            {
                Date = date,
                NewBorrows = borrowRecords.Count(br => 
                    br.BorrowDate.Date == date.Date),
                Returns = borrowRecords.Count(br => 
                    br.ReturnDate.HasValue && br.ReturnDate.Value.Date == date.Date),
                TotalFines = borrowRecords
                    .Where(br => br.Fine.HasValue && br.ReturnDate.HasValue && 
                           br.ReturnDate.Value.Date == date.Date)
                    .Sum(br => br.Fine.Value)
            })
            .ToList();

        return Ok(dailyStats);
    }

    [HttpGet("overdue-books")]
    [Authorize(Policy = "BorrowOperations")]
    public async Task<ActionResult<IEnumerable<BorrowRecordDto>>> GetOverdueBooks()
    {
        var overdueRecords = await _unitOfWork.BorrowRecords.FindAsync(br =>
            br.ReturnDate == null && br.DueDate < DateTime.Now);

        var books = await _unitOfWork.Books.GetAllAsync();
        var members = await _unitOfWork.Members.GetAllAsync();

        var bookDict = books.ToDictionary(b => b.Id, b => b);
        var memberDict = members.ToDictionary(m => m.Id, m => m);

        var overdueDtos = overdueRecords.Select(br => new BorrowRecordDto
        {
            Id = br.Id,
            BookId = br.BookId,
            BookTitle = bookDict.GetValueOrDefault(br.BookId)?.Title ?? "Unknown",
            BookAuthor = bookDict.GetValueOrDefault(br.BookId)?.Author ?? "Unknown",
            MemberId = br.MemberId,
            MemberName = memberDict.GetValueOrDefault(br.MemberId)?.FullName ?? "Unknown",
            BorrowDate = br.BorrowDate,
            DueDate = br.DueDate,
            ReturnDate = br.ReturnDate,
            Status = BorrowStatus.Overdue,
            StatusName = "Quá hạn",
            Notes = br.Notes,
            Fine = br.Fine,
            IsOverdue = true,
            DaysOverdue = br.DaysOverdue,
            CreatedAt = br.CreatedAt
        }).OrderByDescending(br => br.DaysOverdue).ToList();

        return Ok(overdueDtos);
    }

    [HttpGet("fine-collection")]
    [Authorize(Policy = "BorrowOperations")]
    public async Task<ActionResult<object>> GetFineCollection(DateTime? startDate = null, DateTime? endDate = null)
    {
        if (!startDate.HasValue)
        {
            startDate = DateTime.Now.AddMonths(-1);
        }

        if (!endDate.HasValue)
        {
            endDate = DateTime.Now;
        }

        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();
        var fineRecords = borrowRecords
            .Where(br => br.Fine.HasValue && br.Fine.Value > 0 && 
                   br.ReturnDate.HasValue && 
                   br.ReturnDate.Value >= startDate.Value && 
                   br.ReturnDate.Value <= endDate.Value)
            .ToList();

        var totalFine = fineRecords.Sum(br => br.Fine.Value);
        var averageFine = fineRecords.Any() ? fineRecords.Average(br => br.Fine.Value) : 0;
        var maxFine = fineRecords.Any() ? fineRecords.Max(br => br.Fine.Value) : 0;

        // Tạo danh sách chi tiết về phạt
        var fineDetails = new List<object>();
        
        foreach (var br in fineRecords)
        {
            var book = await _unitOfWork.Books.GetByIdAsync(br.BookId);
            var member = await _unitOfWork.Members.GetByIdAsync(br.MemberId);
            
            fineDetails.Add(new
            {
                BorrowId = br.Id,
                BookId = br.BookId,
                BookTitle = book?.Title ?? "Không có thông tin",
                MemberId = br.MemberId,
                MemberName = member?.FullName ?? "Không có thông tin",
                ReturnDate = br.ReturnDate,
                DaysOverdue = (br.ReturnDate.Value - br.DueDate).Days,
                Fine = br.Fine.Value
            });
        }

        var result = new
        {
            StartDate = startDate.Value,
            EndDate = endDate.Value,
            TotalFineCollected = totalFine,
            AverageFine = averageFine,
            MaxFine = maxFine,
            TotalRecords = fineRecords.Count,
            FineDetails = fineDetails
        };

        return Ok(result);
    }

    [HttpPost("custom-report")]
    [Authorize(Policy = "BorrowOperations")]
    public async Task<ActionResult<object>> GenerateCustomReport(ReportRequestDto request)
    {
        // Default dates if not provided
        var startDate = request.StartDate ?? DateTime.Now.AddMonths(-1);
        var endDate = request.EndDate ?? DateTime.Now;

        var borrowRecords = await _unitOfWork.BorrowRecords.GetAllAsync();
        var books = await _unitOfWork.Books.GetAllAsync();
        var members = await _unitOfWork.Members.GetAllAsync();
        var categories = await _unitOfWork.Categories.GetAllAsync();

        // Filter by date range
        var filteredRecords = borrowRecords.Where(br => 
            br.BorrowDate >= startDate && br.BorrowDate <= endDate).ToList();

        // Apply additional filters
        if (request.CategoryId.HasValue)
        {
            var categoryBooks = books.Where(b => b.CategoryId == request.CategoryId.Value)
                                    .Select(b => b.Id).ToList();
            filteredRecords = filteredRecords.Where(br => categoryBooks.Contains(br.BookId)).ToList();
        }

        if (request.MemberId.HasValue)
        {
            filteredRecords = filteredRecords.Where(br => br.MemberId == request.MemberId.Value).ToList();
        }

        if (request.BorrowStatus.HasValue)
        {
            filteredRecords = filteredRecords.Where(br => br.Status == request.BorrowStatus.Value).ToList();
        }

        // Generate report based on type
        object result;

        switch (request.ReportType)
        {
            case ReportType.Daily:
                result = GenerateDailyReport(filteredRecords, startDate, endDate);
                break;
            case ReportType.Monthly:
                result = GenerateMonthlyReport(filteredRecords, startDate, endDate);
                break;
            case ReportType.Yearly:
                result = GenerateYearlyReport(filteredRecords, startDate, endDate);
                break;
            case ReportType.CategoryWise:
                result = GenerateCategoryReport(filteredRecords, books, categories);
                break;
            case ReportType.MemberActivity:
                result = GenerateMemberActivityReport(filteredRecords, members);
                break;
            case ReportType.FineCollection:
                result = GenerateFineReport(filteredRecords);
                break;
            case ReportType.BookStatus:
                result = GenerateBookStatusReport(filteredRecords, books);
                break;
            default:
                return BadRequest("Invalid report type");
        }

        return Ok(new
        {
            ReportType = request.ReportType.ToString(),
            StartDate = startDate,
            EndDate = endDate,
            Data = result
        });
    }

    private object GenerateDailyReport(List<BorrowRecord> records, DateTime startDate, DateTime endDate)
    {
        return Enumerable.Range(0, (endDate - startDate).Days + 1)
            .Select(offset => startDate.AddDays(offset))
            .Select(date => new
            {
                Date = date.ToString("yyyy-MM-dd"),
                Borrows = records.Count(r => r.BorrowDate.Date == date.Date),
                Returns = records.Count(r => r.ReturnDate.HasValue && r.ReturnDate.Value.Date == date.Date),
                Fines = records.Where(r => r.ReturnDate.HasValue && r.ReturnDate.Value.Date == date.Date && r.Fine.HasValue)
                               .Sum(r => r.Fine.Value)
            })
            .ToList();
    }

    private object GenerateMonthlyReport(List<BorrowRecord> records, DateTime startDate, DateTime endDate)
    {
        var startMonth = new DateTime(startDate.Year, startDate.Month, 1);
        var endMonth = new DateTime(endDate.Year, endDate.Month, 1);
        var months = (endMonth.Year - startMonth.Year) * 12 + endMonth.Month - startMonth.Month + 1;

        return Enumerable.Range(0, months)
            .Select(offset => startMonth.AddMonths(offset))
            .Select(month => new
            {
                Month = month.ToString("yyyy-MM"),
                MonthName = month.ToString("MMMM yyyy"),
                Borrows = records.Count(r => r.BorrowDate.Year == month.Year && r.BorrowDate.Month == month.Month),
                Returns = records.Count(r => r.ReturnDate.HasValue && 
                                         r.ReturnDate.Value.Year == month.Year && 
                                         r.ReturnDate.Value.Month == month.Month),
                Fines = records.Where(r => r.ReturnDate.HasValue && 
                                       r.ReturnDate.Value.Year == month.Year && 
                                       r.ReturnDate.Value.Month == month.Month && 
                                       r.Fine.HasValue)
                               .Sum(r => r.Fine.Value)
            })
            .ToList();
    }

    private object GenerateYearlyReport(List<BorrowRecord> records, DateTime startDate, DateTime endDate)
    {
        var startYear = startDate.Year;
        var endYear = endDate.Year;

        return Enumerable.Range(startYear, endYear - startYear + 1)
            .Select(year => new
            {
                Year = year,
                Borrows = records.Count(r => r.BorrowDate.Year == year),
                Returns = records.Count(r => r.ReturnDate.HasValue && r.ReturnDate.Value.Year == year),
                Fines = records.Where(r => r.ReturnDate.HasValue && r.ReturnDate.Value.Year == year && r.Fine.HasValue)
                               .Sum(r => r.Fine.Value)
            })
            .ToList();
    }

    private object GenerateCategoryReport(List<BorrowRecord> records, IEnumerable<Book> books, IEnumerable<Category> categories)
    {
        var bookDict = books.ToDictionary(b => b.Id, b => b);
        var categoryDict = categories.ToDictionary(c => c.Id, c => c.Name);

        var categoryStats = records
            .GroupBy(r => bookDict.GetValueOrDefault(r.BookId)?.CategoryId ?? 0)
            .Select(g => new
            {
                CategoryId = g.Key,
                CategoryName = categoryDict.GetValueOrDefault(g.Key, "Unknown"),
                BorrowCount = g.Count(),
                ReturnCount = g.Count(r => r.ReturnDate.HasValue),
                OverdueCount = g.Count(r => r.IsOverdue),
                TotalFines = g.Where(r => r.Fine.HasValue).Sum(r => r.Fine.Value)
            })
            .OrderByDescending(c => c.BorrowCount)
            .ToList();

        return categoryStats;
    }

    private object GenerateMemberActivityReport(List<BorrowRecord> records, IEnumerable<Member> members)
    {
        var memberDict = members.ToDictionary(m => m.Id, m => m);

        var memberStats = records
            .GroupBy(r => r.MemberId)
            .Select(g => new
            {
                MemberId = g.Key,
                MemberName = memberDict.GetValueOrDefault(g.Key)?.FullName ?? "Unknown",
                Email = memberDict.GetValueOrDefault(g.Key)?.Email ?? "Unknown",
                BorrowCount = g.Count(),
                ReturnCount = g.Count(r => r.ReturnDate.HasValue),
                OverdueCount = g.Count(r => r.IsOverdue),
                TotalFines = g.Where(r => r.Fine.HasValue).Sum(r => r.Fine.Value),
                Status = memberDict.GetValueOrDefault(g.Key)?.Status.ToString() ?? "Unknown"
            })
            .OrderByDescending(m => m.BorrowCount)
            .ToList();

        return memberStats;
    }

    private object GenerateFineReport(List<BorrowRecord> records)
    {
        var fineRecords = records
            .Where(r => r.Fine.HasValue && r.Fine.Value > 0)
            .Select(r => new
            {
                BorrowId = r.Id,
                MemberId = r.MemberId,
                BookId = r.BookId,
                BorrowDate = r.BorrowDate,
                DueDate = r.DueDate,
                ReturnDate = r.ReturnDate,
                DaysOverdue = r.DaysOverdue,
                Fine = r.Fine.Value
            })
            .OrderByDescending(r => r.Fine)
            .ToList();

        return new
        {
            TotalFines = fineRecords.Sum(r => r.Fine),
            AverageFine = fineRecords.Any() ? fineRecords.Average(r => r.Fine) : 0,
            MaxFine = fineRecords.Any() ? fineRecords.Max(r => r.Fine) : 0,
            FineRecords = fineRecords
        };
    }

    private object GenerateBookStatusReport(List<BorrowRecord> records, IEnumerable<Book> books)
    {
        var bookDict = books.ToDictionary(b => b.Id, b => b);

        var bookStats = records
            .GroupBy(r => r.BookId)
            .Select(g => new
            {
                BookId = g.Key,
                Title = bookDict.GetValueOrDefault(g.Key)?.Title ?? "Unknown",
                Author = bookDict.GetValueOrDefault(g.Key)?.Author ?? "Unknown",
                BorrowCount = g.Count(),
                ReturnCount = g.Count(r => r.ReturnDate.HasValue),
                OverdueCount = g.Count(r => r.IsOverdue),
                LostCount = g.Count(r => r.Status == BorrowStatus.Lost),
                AverageBorrowDays = g.Where(r => r.ReturnDate.HasValue)
                                     .Average(r => (r.ReturnDate.Value - r.BorrowDate).TotalDays)
            })
            .OrderByDescending(b => b.BorrowCount)
            .ToList();

        return bookStats;
    }
}
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
// import { MatChipsModule } from '@angular/material/chips'; // Not available in Angular 18

import { ShelfService } from '../../services/shelf.service';
import { ZoneService, Zone } from '../../services/zone.service';
import { 
  CreateShelf, 
  UpdateShelf, 
  ShelfStatus, 
  ShelfValidation 
} from '../../models/shelf.model';

@Component({
  selector: 'app-shelf-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    // MatChipsModule // Not available
  ],
  template: `
    <div class="shelf-form-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>
            {{ isEditMode ? 'Chỉnh sửa Kệ Sách' : 'Thêm Kệ Sách Mới' }}
          </mat-card-title>
          <mat-card-subtitle>
            {{ isEditMode ? 'Cập nhật thông tin kệ sách' : 'Tạo kệ sách mới trong thư viện' }}
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="shelfForm" (ngSubmit)="onSubmit()" class="shelf-form">
            <!-- Tên kệ -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Tên kệ sách *</mat-label>
              <input matInput formControlName="name" placeholder="VD: Kệ A1, Kệ Văn học...">
              <mat-icon matSuffix>label</mat-icon>
              <mat-error *ngIf="shelfForm.get('name')?.hasError('required')">
                Tên kệ là bắt buộc
              </mat-error>
              <mat-error *ngIf="shelfForm.get('name')?.hasError('minlength')">
                Tên kệ phải có ít nhất 2 ký tự
              </mat-error>
              <mat-error *ngIf="shelfForm.get('name')?.hasError('maxlength')">
                Tên kệ không được quá 100 ký tự
              </mat-error>
            </mat-form-field>

            <!-- Khu vực -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Khu vực *</mat-label>
              <mat-select formControlName="zoneId" placeholder="Chọn khu vực">
                <mat-option *ngFor="let zone of zones" [value]="zone.id">
                  {{ zone.name }}
                  <span class="zone-description" *ngIf="zone.description">
                    - {{ zone.description }}
                  </span>
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>location_on</mat-icon>
              <mat-error *ngIf="shelfForm.get('zoneId')?.hasError('required')">
                Vui lòng chọn khu vực
              </mat-error>
            </mat-form-field>

            <!-- Sức chứa -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Sức chứa *</mat-label>
              <input matInput type="number" formControlName="capacity" 
                     placeholder="Số lượng sách tối đa" min="1" max="1000">
              <mat-icon matSuffix>inventory</mat-icon>
              <mat-hint>Số lượng sách tối đa có thể chứa</mat-hint>
              <mat-error *ngIf="shelfForm.get('capacity')?.hasError('required')">
                Sức chứa là bắt buộc
              </mat-error>
              <mat-error *ngIf="shelfForm.get('capacity')?.hasError('min')">
                Sức chứa phải lớn hơn 0
              </mat-error>
              <mat-error *ngIf="shelfForm.get('capacity')?.hasError('max')">
                Sức chứa không được quá 1000
              </mat-error>
            </mat-form-field>

            <!-- Trạng thái -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Trạng thái</mat-label>
              <mat-select formControlName="status">
                <mat-option [value]="ShelfStatus.ACTIVE">
                  <mat-icon class="status-icon active">check_circle</mat-icon>
                  Hoạt động
                </mat-option>
                <mat-option [value]="ShelfStatus.INACTIVE">
                  <mat-icon class="status-icon inactive">cancel</mat-icon>
                  Không hoạt động
                </mat-option>
                <mat-option [value]="ShelfStatus.MAINTENANCE">
                  <mat-icon class="status-icon maintenance">build</mat-icon>
                  Bảo trì
                </mat-option>
              </mat-select>
              <mat-icon matSuffix>info</mat-icon>
            </mat-form-field>

            <!-- Mô tả -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Mô tả</mat-label>
              <textarea matInput formControlName="description" 
                        placeholder="Mô tả chi tiết về kệ sách..."
                        rows="3" maxlength="500"></textarea>
              <mat-icon matSuffix>description</mat-icon>
              <mat-hint align="end">
                {{ shelfForm.get('description')?.value?.length || 0 }}/500
              </mat-hint>
            </mat-form-field>

            <!-- Validation errors -->
            <div *ngIf="validationErrors.length > 0" class="validation-errors">
              <div class="validation-chips">
                <span *ngFor="let error of validationErrors" class="error-chip">
                  <mat-icon>error</mat-icon>
                  {{ error }}
                </span>
              </div>
            </div>

            <!-- Validation warnings -->
            <div *ngIf="validationWarnings.length > 0" class="validation-warnings">
              <div class="validation-chips">
                <span *ngFor="let warning of validationWarnings" class="warning-chip">
                  <mat-icon>warning</mat-icon>
                  {{ warning }}
                </span>
              </div>
            </div>
          </form>
        </mat-card-content>

        <mat-card-actions align="end">
          <button mat-button type="button" (click)="onCancel()">
            <mat-icon>cancel</mat-icon>
            Hủy
          </button>
          
          <button mat-raised-button color="primary" 
                  (click)="onSubmit()" 
                  [disabled]="!shelfForm.valid || isLoading">
            <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
            <mat-icon *ngIf="!isLoading">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
            {{ isEditMode ? 'Cập nhật' : 'Tạo mới' }}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .shelf-form-container {
      max-width: 600px;
      margin: 20px auto;
      padding: 0 16px;
    }

    .shelf-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .full-width {
      width: 100%;
    }

    .zone-description {
      color: rgba(0, 0, 0, 0.6);
      font-size: 0.9em;
    }

    .status-icon {
      margin-right: 8px;
      vertical-align: middle;
    }

    .status-icon.active { color: #4caf50; }
    .status-icon.inactive { color: #f44336; }
    .status-icon.maintenance { color: #ff9800; }

    .validation-errors, .validation-warnings {
      margin: 16px 0;
    }

    .validation-chips {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .error-chip, .warning-chip {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
    }

    .error-chip {
      background-color: #ffebee;
      color: #c62828;
    }

    .warning-chip {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    mat-card-header {
      margin-bottom: 16px;
    }

    mat-card-actions {
      padding: 16px;
      gap: 8px;
    }
  `]
})
export class ShelfFormComponent implements OnInit {
  shelfForm: FormGroup;
  zones: Zone[] = [];
  isEditMode = false;
  isLoading = false;
  shelfId?: number;

  validationErrors: string[] = [];
  validationWarnings: string[] = [];

  // Expose enum to template
  ShelfStatus = ShelfStatus;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private shelfService: ShelfService,
    private zoneService: ZoneService,
    private snackBar: MatSnackBar
  ) {
    this.shelfForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadZones();
    this.checkEditMode();
    this.setupFormValidation();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      zoneId: [null, [Validators.required]],
      capacity: [20, [Validators.required, Validators.min(1), Validators.max(1000)]],
      status: [ShelfStatus.ACTIVE],
      description: ['', [Validators.maxLength(500)]]
    });
  }

  private setupFormValidation(): void {
    // Real-time validation
    this.shelfForm.valueChanges.subscribe(() => {
      this.validationErrors = [];
      this.validationWarnings = [];
    });
  }

  private loadZones(): void {
    this.zoneService.getAllZones().subscribe({
      next: (zones) => {
        this.zones = zones;
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi tải danh sách khu vực: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.shelfId = parseInt(id);
      this.loadShelfData();
    }
  }

  private loadShelfData(): void {
    if (!this.shelfId) return;
    
    this.isLoading = true;
    this.shelfService.getShelfById(this.shelfId).subscribe({
      next: (shelf) => {
        this.shelfForm.patchValue({
          name: shelf.name,
          zoneId: shelf.zoneId,
          capacity: shelf.capacity,
          status: shelf.status,
          description: shelf.description
        });
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi tải thông tin kệ: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.router.navigate(['/shelves']);
      }
    });
  }

  validateForm(): void {
    if (!this.shelfForm.valid) {
      this.markFormGroupTouched();
      return;
    }

    const shelfData = this.isEditMode 
      ? { ...this.shelfForm.value, id: this.shelfId } as UpdateShelf
      : this.shelfForm.value as CreateShelf;

    this.shelfService.validateShelf(shelfData).subscribe({
      next: (validation: ShelfValidation) => {
        this.validationErrors = validation.errors || [];
        this.validationWarnings = validation.warnings || [];
        
        if (validation.isValid) {
          this.snackBar.open('Dữ liệu hợp lệ!', 'Đóng', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        }
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi kiểm tra dữ liệu: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onSubmit(): void {
    if (!this.shelfForm.valid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;
    
    if (this.isEditMode) {
      this.updateShelf();
    } else {
      this.createShelf();
    }
  }

  private createShelf(): void {
    const shelfData: CreateShelf = this.shelfForm.value;
    
    this.shelfService.createShelf(shelfData).subscribe({
      next: (response) => {
        this.snackBar.open('Tạo kệ sách thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.router.navigate(['/shelves']);
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi tạo kệ: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
      }
    });
  }

  private updateShelf(): void {
    if (!this.shelfId) return;
    
    const shelfData: UpdateShelf = { ...this.shelfForm.value, id: this.shelfId };
    
    this.shelfService.updateShelf(this.shelfId, shelfData).subscribe({
      next: (response) => {
        this.snackBar.open('Cập nhật kệ sách thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.router.navigate(['/shelves']);
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi cập nhật kệ: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/shelves']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.shelfForm.controls).forEach(key => {
      const control = this.shelfForm.get(key);
      control?.markAsTouched();
    });
  }
}

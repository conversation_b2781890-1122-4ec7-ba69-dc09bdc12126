import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { CategoryService } from '../../services/category.service';
import { CreateCategory, UpdateCategory, CategoryValidation } from '../../models/category.model';

@Component({
  selector: 'app-category-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="category-form-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>
            {{ isEditMode ? 'Chỉnh sửa Thể loại' : 'Thêm Thể loại Mới' }}
          </mat-card-title>
          <mat-card-subtitle>
            {{ isEditMode ? 'Cập nhật thông tin thể loại' : 'Tạo thể loại mới cho sách' }}
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="categoryForm" (ngSubmit)="onSubmit()" class="category-form">
            <!-- Tên thể loại -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Tên thể loại *</mat-label>
              <input matInput formControlName="name" 
                     placeholder="VD: Văn học, Khoa học, Lịch sử...">
              <mat-icon matSuffix>label</mat-icon>
              <mat-error *ngIf="categoryForm.get('name')?.hasError('required')">
                Tên thể loại là bắt buộc
              </mat-error>
              <mat-error *ngIf="categoryForm.get('name')?.hasError('minlength')">
                Tên thể loại phải có ít nhất 2 ký tự
              </mat-error>
              <mat-error *ngIf="categoryForm.get('name')?.hasError('maxlength')">
                Tên thể loại không được quá 200 ký tự
              </mat-error>
            </mat-form-field>

            <!-- Mô tả -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Mô tả</mat-label>
              <textarea matInput formControlName="description" 
                        placeholder="Mô tả chi tiết về thể loại này..."
                        rows="4" maxlength="1000"></textarea>
              <mat-icon matSuffix>description</mat-icon>
              <mat-hint align="end">
                {{ categoryForm.get('description')?.value?.length || 0 }}/1000
              </mat-hint>
              <mat-error *ngIf="categoryForm.get('description')?.hasError('maxlength')">
                Mô tả không được quá 1000 ký tự
              </mat-error>
            </mat-form-field>

            <!-- Validation errors -->
            <div *ngIf="validationErrors.length > 0" class="validation-errors">
              <div class="validation-chips">
                <span *ngFor="let error of validationErrors" class="error-chip">
                  <mat-icon>error</mat-icon>
                  {{ error }}
                </span>
              </div>
            </div>

            <!-- Validation warnings -->
            <div *ngIf="validationWarnings.length > 0" class="validation-warnings">
              <div class="validation-chips">
                <span *ngFor="let warning of validationWarnings" class="warning-chip">
                  <mat-icon>warning</mat-icon>
                  {{ warning }}
                </span>
              </div>
            </div>
          </form>
        </mat-card-content>

        <mat-card-actions align="end">
          <button mat-button type="button" (click)="onCancel()">
            <mat-icon>cancel</mat-icon>
            Hủy
          </button>
          
          <button mat-raised-button color="primary" 
                  (click)="onSubmit()" 
                  [disabled]="!categoryForm.valid || isLoading">
            <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
            <mat-icon *ngIf="!isLoading">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
            {{ isEditMode ? 'Cập nhật' : 'Tạo mới' }}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .category-form-container {
      max-width: 600px;
      margin: 20px auto;
      padding: 0 16px;
    }

    .category-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .full-width {
      width: 100%;
    }

    .validation-errors, .validation-warnings {
      margin: 16px 0;
    }

    .validation-chips {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .error-chip, .warning-chip {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
    }

    .error-chip {
      background-color: #ffebee;
      color: #c62828;
    }

    .warning-chip {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    mat-card-header {
      margin-bottom: 16px;
    }

    mat-card-actions {
      padding: 16px;
      gap: 8px;
    }
  `]
})
export class CategoryFormComponent implements OnInit {
  categoryForm: FormGroup;
  isEditMode = false;
  isLoading = false;
  categoryId?: number;
  
  validationErrors: string[] = [];
  validationWarnings: string[] = [];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private categoryService: CategoryService,
    private snackBar: MatSnackBar
  ) {
    this.categoryForm = this.createForm();
  }

  ngOnInit(): void {
    this.checkEditMode();
    this.setupFormValidation();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(200)]],
      description: ['', [Validators.maxLength(1000)]]
    });
  }

  private setupFormValidation(): void {
    // Real-time validation
    this.categoryForm.valueChanges.subscribe(() => {
      this.validationErrors = [];
      this.validationWarnings = [];
    });
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.categoryId = parseInt(id);
      this.loadCategoryData();
    }
  }

  private loadCategoryData(): void {
    if (!this.categoryId) return;
    
    this.isLoading = true;
    this.categoryService.getCategoryById(this.categoryId).subscribe({
      next: (category) => {
        this.categoryForm.patchValue({
          name: category.name,
          description: category.description
        });
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi tải thông tin thể loại: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.router.navigate(['/categories']);
      }
    });
  }

  validateForm(): void {
    if (!this.categoryForm.valid) {
      this.markFormGroupTouched();
      return;
    }

    const categoryData = this.categoryForm.value as CreateCategory;

    this.categoryService.validateCategory(categoryData).subscribe({
      next: (validation: CategoryValidation) => {
        this.validationErrors = validation.errors || [];
        this.validationWarnings = validation.warnings || [];
        
        if (validation.isValid) {
          this.snackBar.open('Dữ liệu hợp lệ!', 'Đóng', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        }
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi kiểm tra dữ liệu: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onSubmit(): void {
    if (!this.categoryForm.valid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;
    
    if (this.isEditMode) {
      this.updateCategory();
    } else {
      this.createCategory();
    }
  }

  private createCategory(): void {
    const categoryData: CreateCategory = this.categoryForm.value;
    
    this.categoryService.createCategory(categoryData).subscribe({
      next: (response) => {
        this.snackBar.open('Tạo thể loại thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.router.navigate(['/categories']);
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi tạo thể loại: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
      }
    });
  }

  private updateCategory(): void {
    if (!this.categoryId) return;
    
    const categoryData: UpdateCategory = { ...this.categoryForm.value, id: this.categoryId };
    
    this.categoryService.updateCategory(this.categoryId, categoryData).subscribe({
      next: (response) => {
        this.snackBar.open('Cập nhật thể loại thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.router.navigate(['/categories']);
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi cập nhật thể loại: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/categories']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.categoryForm.controls).forEach(key => {
      const control = this.categoryForm.get(key);
      control?.markAsTouched();
    });
  }
}

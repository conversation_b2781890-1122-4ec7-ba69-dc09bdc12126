using System.Linq.Expressions;
using LibraryManagement.Core.Entities;
using LibraryManagement.Core.Interfaces;
using LibraryManagement.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace LibraryManagement.Infrastructure.Repositories
{
    public class BookRepository : Repository<Book>, IBookRepository
    {
        private readonly LibraryDbContext _dbContext;

        public BookRepository(LibraryDbContext context) : base(context)
        {
            _dbContext = context;
        }

        public async Task<IEnumerable<Book>> GetAllWithCategoriesAsync()
        {
            return await _dbContext.Books
                .Include(b => b.Category)
                .ToListAsync();
        }
    }
} 
import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { MatCardModule } from "@angular/material/card";
import { MatTableModule } from "@angular/material/table";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatSelectModule } from "@angular/material/select";
import { MatDialogModule, MatDialog } from "@angular/material/dialog";
import { MatSnackBarModule, MatSnackBar } from "@angular/material/snack-bar";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatChipsModule } from "@angular/material/chips";
import {
  BorrowService,
  BorrowRecord,
  BorrowStatus,
  BorrowSearchParams,
} from "../../services/borrow.service";
import { BookService } from "../../services/book.service";
import { MemberService } from "../../services/member.service";
import { AuthService } from "../../services/auth.service";
import { HasPermissionDirective } from "../../directives/has-permission.directive";
import { BorrowModalComponent } from "./borrow-modal.component";
import { ReturnModalComponent } from "./return-modal.component";

@Component({
  selector: "app-borrow-list",
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatCardModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatChipsModule,
    HasPermissionDirective,
  ],
  template: `
    <div class="borrow-list-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>library_books</mat-icon>
            Quản lý Mượn/Trả Sách
          </mat-card-title>
          <mat-card-subtitle>
            Tổng số giao dịch: {{ borrowRecords.length }}
            <span *ngIf="overdueCount > 0" class="overdue-warning">
              | Quá hạn: {{ overdueCount }}
            </span>
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <!-- Search and Filter Section -->
          <div class="search-section">
            <div class="search-row">
              <mat-form-field appearance="outline" class="search-field">
                <mat-label>Tên thành viên</mat-label>
                <input
                  matInput
                  [(ngModel)]="searchParams.memberName"
                  (input)="onSearchChange()"
                  placeholder="Nhập tên thành viên"
                />
                <mat-icon matSuffix>person</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="search-field">
                <mat-label>Tên sách</mat-label>
                <input
                  matInput
                  [(ngModel)]="searchParams.bookTitle"
                  (input)="onSearchChange()"
                  placeholder="Nhập tên sách"
                />
                <mat-icon matSuffix>book</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="search-field">
                <mat-label>Trạng thái</mat-label>
                <mat-select
                  [(ngModel)]="searchParams.status"
                  (selectionChange)="onSearchChange()"
                >
                  <mat-option [value]="undefined">Tất cả</mat-option>
                  <mat-option [value]="BorrowStatus.Borrowed"
                    >Đang mượn</mat-option
                  >
                  <mat-option [value]="BorrowStatus.Returned"
                    >Đã trả</mat-option
                  >
                  <mat-option [value]="BorrowStatus.Overdue"
                    >Quá hạn</mat-option
                  >
                  <mat-option [value]="BorrowStatus.Lost">Mất sách</mat-option>
                  <mat-option [value]="BorrowStatus.Renewed"
                    >Gia hạn</mat-option
                  >
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline" class="search-field">
                <mat-label>Quá hạn</mat-label>
                <mat-select
                  [(ngModel)]="searchParams.overdue"
                  (selectionChange)="onSearchChange()"
                >
                  <mat-option [value]="undefined">Tất cả</mat-option>
                  <mat-option [value]="true">Chỉ quá hạn</mat-option>
                  <mat-option [value]="false">Không quá hạn</mat-option>
                </mat-select>
              </mat-form-field>

              <button
                mat-icon-button
                (click)="clearSearch()"
                matTooltip="Xóa bộ lọc"
              >
                <mat-icon>clear</mat-icon>
              </button>
            </div>

            <div class="toolbar-actions">
              <button
                mat-raised-button
                color="primary"
                (click)="openBorrowDialog()"
                *appHasPermission="'borrow-operations'"
              >
                <mat-icon>add</mat-icon>
                Cho mượn sách
              </button>
            </div>
          </div>

          <!-- Results Table -->
          <div class="table-container" *ngIf="!loading">
            <table mat-table [dataSource]="borrowRecords" class="borrow-table">
              <!-- STT Column -->
              <ng-container matColumnDef="stt">
                <th mat-header-cell *matHeaderCellDef>STT</th>
                <td mat-cell *matCellDef="let record; let i = index">{{ i + 1 }}</td>
              </ng-container>

              <!-- Member Column -->
              <ng-container matColumnDef="member">
                <th mat-header-cell *matHeaderCellDef>Thành viên</th>
                <td mat-cell *matCellDef="let record">
                  <div class="member-info">
                    <strong>{{ record.memberName }}</strong>
                  </div>
                </td>
              </ng-container>

              <!-- Book Column -->
              <ng-container matColumnDef="book">
                <th mat-header-cell *matHeaderCellDef>Sách</th>
                <td mat-cell *matCellDef="let record">
                  <div class="book-info">
                    <strong>{{ record.bookTitle }}</strong>
                    <br />
                    <small class="author">{{ record.bookAuthor }}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Borrow Date Column -->
              <ng-container matColumnDef="borrowDate">
                <th mat-header-cell *matHeaderCellDef>Ngày mượn</th>
                <td mat-cell *matCellDef="let record">
                  {{ formatDate(record.borrowDate) }}
                </td>
              </ng-container>

              <!-- Due Date Column -->
              <ng-container matColumnDef="dueDate">
                <th mat-header-cell *matHeaderCellDef>Hạn trả</th>
                <td mat-cell *matCellDef="let record">
                  <span [class]="getDueDateClass(record)">
                    {{ formatDate(record.dueDate) }}
                  </span>
                  <mat-icon
                    *ngIf="record.isOverdue"
                    class="overdue-icon"
                    matTooltip="Quá hạn {{ record.daysOverdue }} ngày"
                  >
                    warning
                  </mat-icon>
                </td>
              </ng-container>

              <!-- Return Date Column -->
              <ng-container matColumnDef="returnDate">
                <th mat-header-cell *matHeaderCellDef>Ngày trả</th>
                <td mat-cell *matCellDef="let record">
                  <span *ngIf="record.returnDate; else notReturned">
                    {{ formatDate(record.returnDate) }}
                  </span>
                  <ng-template #notReturned>
                    <span class="not-returned">Chưa trả</span>
                  </ng-template>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Trạng thái</th>
                <td mat-cell *matCellDef="let record">
                  <mat-chip
                    [class]="borrowService.getStatusColorClass(record.status)"
                  >
                    {{ record.statusName }}
                  </mat-chip>
                </td>
              </ng-container>

              <!-- Fine Column -->
              <ng-container matColumnDef="fine">
                <th mat-header-cell *matHeaderCellDef>Phạt</th>
                <td mat-cell *matCellDef="let record">
                  <span *ngIf="record.fine" class="fine-amount">
                    {{ formatCurrency(record.fine) }}
                  </span>
                  <span *ngIf="!record.fine">-</span>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Thao tác</th>
                <td mat-cell *matCellDef="let record">
                  <div class="table-actions">
                    <button
                      mat-icon-button
                      (click)="viewDetails(record)"
                      matTooltip="Xem chi tiết"
                    >
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button
                      mat-icon-button
                      (click)="openReturnDialog(record)"
                      matTooltip="Trả sách"
                      color="primary"
                      *ngIf="!record.returnDate && canHandleBorrowOperations()"
                    >
                      <mat-icon>assignment_return</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr
                mat-row
                *matRowDef="let row; columns: displayedColumns"
                [class.overdue-row]="row.isOverdue && !row.returnDate"
                [class.returned-row]="row.returnDate"
              ></tr>
            </table>

            <div *ngIf="borrowRecords.length === 0" class="no-data">
              <mat-icon>info</mat-icon>
              <p>Không có dữ liệu</p>
            </div>
          </div>

          <div *ngIf="loading" class="loading">
            <p>Đang tải dữ liệu...</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [
    `
      .borrow-list-container {
        padding: 20px;
      }

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .overdue-warning {
        color: #f44336;
        font-weight: bold;
      }

      .search-section {
        margin-bottom: 20px;
      }

      .search-row {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 16px;
      }

      .search-field {
        min-width: 200px;
        flex: 1;
      }

      .toolbar-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
      }

      .table-actions {
        display: flex;
        gap: 8px;
        align-items: center;
      }

      .has-overdue {
        background-color: #ffebee !important;
        color: #c62828;
      }

      .table-container {
        overflow-x: auto;
      }

      .borrow-table {
        width: 100%;
        min-width: 800px;
      }

      .member-info,
      .book-info {
        line-height: 1.4;
      }

      .author {
        color: #666;
      }

      .overdue-row {
        background-color: #ffebee;
      }

      .returned-row {
        background-color: #e8f5e8;
      }

      .due-date-normal {
        color: #4caf50;
      }

      .due-date-warning {
        color: #ff9800;
        font-weight: bold;
      }

      .due-date-overdue {
        color: #f44336;
        font-weight: bold;
      }

      .overdue-icon {
        color: #f44336;
        font-size: 16px;
        margin-left: 4px;
      }

      .not-returned {
        color: #666;
        font-style: italic;
      }

      .fine-amount {
        color: #f44336;
        font-weight: bold;
      }

      .status-borrowed {
        background-color: #2196f3;
        color: white;
      }

      .status-returned {
        background-color: #4caf50;
        color: white;
      }

      .status-overdue {
        background-color: #f44336;
        color: white;
      }

      .status-lost {
        background-color: #9c27b0;
        color: white;
      }

      .status-renewed {
        background-color: #ff9800;
        color: white;
      }

      .no-data {
        text-align: center;
        padding: 40px;
        color: #666;
      }

      .no-data mat-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      .loading {
        text-align: center;
        padding: 40px;
      }

      @media (max-width: 768px) {
        .search-row {
          flex-direction: column;
        }

        .search-field {
          width: 100%;
        }

        .action-buttons {
          justify-content: center;
        }
      }
    `,
  ],
})
export class BorrowListComponent implements OnInit {
  borrowRecords: BorrowRecord[] = [];
  displayedColumns: string[] = [
    "stt",
    "member",
    "book",
    "borrowDate",
    "dueDate",
    "returnDate",
    "status",
    "fine",
    "actions",
  ];
  loading = false;
  searchParams: BorrowSearchParams = {};
  overdueCount = 0;
  BorrowStatus = BorrowStatus;

  constructor(
    public borrowService: BorrowService,
    private bookService: BookService,
    private memberService: MemberService,
    private authService: AuthService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  canHandleBorrowOperations(): boolean {
    return this.authService.canHandleBorrowOperations();
  }

  ngOnInit(): void {
    this.loadBorrowRecords();
    this.loadOverdueCount();
  }

  loadBorrowRecords(): void {
    this.loading = true;
    this.borrowService.getBorrowRecords().subscribe({
      next: (records) => {
        this.borrowRecords = records.sort(
          (a, b) =>
            new Date(a.borrowDate).getTime() - new Date(b.borrowDate).getTime()
        );
        this.loading = false;
      },
      error: (error) => {
        console.error("Error loading borrow records:", error);
        this.snackBar.open("Lỗi khi tải danh sách mượn/trả", "Đóng", {
          duration: 3000,
        });
        this.loading = false;
      },
    });
  }

  loadOverdueCount(): void {
    this.borrowService.getOverdueBooks().subscribe({
      next: (records) => {
        this.overdueCount = records.length;
      },
      error: (error) => {
        console.error("Error loading overdue count:", error);
      },
    });
  }

  loadOverdueBooks(): void {
    this.loading = true;
    this.borrowService.getOverdueBooks().subscribe({
      next: (records) => {
        this.borrowRecords = records.sort(
          (a, b) =>
            new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
        );
        this.overdueCount = records.length;
        this.loading = false;
        this.snackBar.open(`Tìm thấy ${records.length} sách quá hạn`, "Đóng", {
          duration: 3000,
        });
      },
      error: (error) => {
        console.error("Error loading overdue books:", error);
        this.snackBar.open("Lỗi khi tải danh sách sách quá hạn", "Đóng", {
          duration: 3000,
        });
        this.loading = false;
      },
    });
  }

  onSearchChange(): void {
    if (this.hasSearchParams()) {
      this.searchBorrowRecords();
    } else {
      this.loadBorrowRecords();
    }
  }

  searchBorrowRecords(): void {
    this.loading = true;
    this.borrowService.searchBorrowRecords(this.searchParams).subscribe({
      next: (records) => {
        this.borrowRecords = records;
        this.loading = false;
      },
      error: (error) => {
        console.error("Error searching borrow records:", error);
        this.snackBar.open("Lỗi khi tìm kiếm", "Đóng", { duration: 3000 });
        this.loading = false;
      },
    });
  }

  hasSearchParams(): boolean {
    return !!(
      this.searchParams.memberName ||
      this.searchParams.bookTitle ||
      this.searchParams.status !== undefined ||
      this.searchParams.overdue !== undefined
    );
  }

  clearSearch(): void {
    this.searchParams = {};
    this.loadBorrowRecords();
  }

  openBorrowDialog(): void {
    const dialogRef = this.dialog.open(BorrowModalComponent, {
      width: "600px",
      maxWidth: "90vw",
      disableClose: true,
      data: {},
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Refresh the list after successful borrow
        this.loadBorrowRecords();
        this.loadOverdueCount();
      }
    });
  }

  openReturnDialog(record: BorrowRecord): void {
    const dialogRef = this.dialog.open(ReturnModalComponent, {
      width: "700px",
      maxWidth: "90vw",
      disableClose: true,
      data: { borrowRecord: record },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Refresh the list after successful return
        this.loadBorrowRecords();
        this.loadOverdueCount();
      }
    });
  }

  viewDetails(record: BorrowRecord): void {
    // Create a simple details dialog
    const dialogRef = this.dialog.open(ReturnModalComponent, {
      width: "700px",
      maxWidth: "90vw",
      data: { borrowRecord: record, viewOnly: true },
    });

    // For now, just show the record details in a snackbar
    const details = [
      `Sách: ${record.bookTitle}`,
      `Thành viên: ${record.memberName}`,
      `Ngày mượn: ${this.formatDate(record.borrowDate)}`,
      `Hạn trả: ${this.formatDate(record.dueDate)}`,
      record.returnDate
        ? `Ngày trả: ${this.formatDate(record.returnDate)}`
        : "Chưa trả",
      `Trạng thái: ${record.statusName}`,
    ].join(" | ");

    this.snackBar.open(details, "Đóng", { duration: 8000 });
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString("vi-VN");
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  }

  getDueDateClass(record: BorrowRecord): string {
    if (record.returnDate) return "due-date-normal";

    const dueDate = new Date(record.dueDate);
    const today = new Date();
    const diffDays = Math.ceil(
      (dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (diffDays < 0) return "due-date-overdue";
    if (diffDays <= 3) return "due-date-warning";
    return "due-date-normal";
  }
}

-- Script để đồng bộ số lượng sách trên kệ
-- Thay thế cho migrate-data.html

USE LibraryManagementDb;
GO

PRINT 'Bắt đầu đồng bộ số lượng sách trên kệ...';

-- 1. <PERSON><PERSON><PERSON> bảng tạm để lưu thông tin số lượng sách trên từng kệ
IF OBJECT_ID('tempdb..#ShelfBookCounts') IS NOT NULL
    DROP TABLE #ShelfBookCounts;

CREATE TABLE #ShelfBookCounts (
    BookId INT,
    BookshelfId INT,
    TotalQuantity INT,
    BorrowedQuantity INT
);

-- 2. T<PERSON>h toán số lượng sách trên từng kệ dựa trên ShelfSlotBooks
INSERT INTO #ShelfBookCounts (BookId, BookshelfId, TotalQuantity, BorrowedQuantity)
SELECT 
    SSB.BookId,
    SS.BookshelfId,
    SUM(SSB.Quantity) AS TotalQuantity,
    SUM(SSB.BorrowQuantity) AS BorrowedQuantity
FROM ShelfSlotBooks SSB
JOIN ShelfSlots SS ON SSB.ShelfSlotId = SS.Id
GROUP BY SSB.BookId, SS.BookshelfId;

PRINT 'Đã tính toán số lượng sách trên từng kệ';

-- 3. Cập nhật thông tin BookshelfId và LocationCode cho Books
UPDATE B
SET 
    B.BookshelfId = SBC.BookshelfId,
    B.LocationCode = (
        SELECT TOP 1 SS.SlotCode
        FROM ShelfSlotBooks SSB
        JOIN ShelfSlots SS ON SSB.ShelfSlotId = SS.Id
        WHERE SSB.BookId = B.Id AND SS.BookshelfId = SBC.BookshelfId
        ORDER BY SSB.Quantity DESC
    )
FROM Books B
JOIN #ShelfBookCounts SBC ON B.Id = SBC.BookId
WHERE SBC.TotalQuantity > 0;

PRINT 'Đã cập nhật BookshelfId và LocationCode cho Books';

-- 4. Cập nhật OnShelfQuantity cho Books
UPDATE B
SET 
    B.OnShelfQuantity = SBC.TotalQuantity - SBC.BorrowedQuantity,
    B.BorrowedQuantity = SBC.BorrowedQuantity
FROM Books B
JOIN #ShelfBookCounts SBC ON B.Id = SBC.BookId;

PRINT 'Đã cập nhật OnShelfQuantity và BorrowedQuantity cho Books';

-- 5. Đảm bảo tính nhất quán giữa StockQuantity, OnShelfQuantity và Quantity
UPDATE Books
SET Quantity = StockQuantity + OnShelfQuantity;

PRINT 'Đã đảm bảo tính nhất quán giữa StockQuantity, OnShelfQuantity và Quantity';

-- 6. Cập nhật thông tin vị trí sách trong BorrowRecords
UPDATE BR
SET 
    BR.BookshelfId = B.BookshelfId,
    BR.LocationCode = B.LocationCode
FROM BorrowRecords BR
JOIN Books B ON BR.BookId = B.Id
WHERE B.BookshelfId IS NOT NULL
AND BR.ReturnDate IS NULL
AND BR.BookshelfId IS NULL;

PRINT 'Đã cập nhật thông tin vị trí sách trong BorrowRecords';

-- 7. Xóa bảng tạm
DROP TABLE #ShelfBookCounts;

PRINT 'Đồng bộ số lượng sách trên kệ hoàn tất!';
GO 
using LibraryManagement.Core.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using LibraryManagement.Application.DTOs;
using LibraryManagement.Core.Entities;
using Microsoft.AspNetCore.Authorization;

namespace LibraryManagement.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [AllowAnonymous] // Allow anonymous access for all endpoints
    public class ZonesController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;

        public ZonesController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var zones = await _unitOfWork.Zones.Query()
                .Select(z => new ZoneDto
                {
                    Id = z.Id,
                    Name = z.Name,
                    Description = z.Description
                }).ToListAsync();

            return Ok(zones);
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] ZoneDto dto)
        {
            if (string.IsNullOrWhiteSpace(dto.Name))
            {
                return BadRequest("Tên khu vực là bắt buộc.");
            }

            var zone = new Zone
            {
                Name = dto.Name,
                Description = dto.Description,
                CreatedAt = DateTime.Now
            };

            await _unitOfWork.Zones.AddAsync(zone);
            await _unitOfWork.SaveChangesAsync();

            return Ok(new { message = "Đã thêm khu vực thành công", zone.Id });
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] ZoneDto dto)
        {
            var zone = await _unitOfWork.Zones.GetByIdAsync(id);
            if (zone == null)
                return NotFound();

            zone.Name = dto.Name!;
            zone.Description = dto.Description;
            zone.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.SaveAsync();

            return Ok(new { message = "Cập nhật thành công" });
        }
    }
}

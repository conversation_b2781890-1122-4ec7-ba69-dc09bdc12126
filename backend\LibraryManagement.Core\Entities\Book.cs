namespace LibraryManagement.Core.Entities;

public class Book : BaseEntity
{
    public string Title { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string? ISBN { get; set; }
    public string? Publisher { get; set; }
    public DateTime? PublishedDate { get; set; }
    public int CategoryId { get; set; }
    
    // Số lượng tổng - chỉ đọc, đ<PERSON><PERSON><PERSON> tính từ StockQuantity + OnShelfQuantity
    public int Quantity => StockQuantity + OnShelfQuantity;
    
    // Số lượng trong kho
    public int StockQuantity { get; set; }
    
    // Số lượng trên kệ
    public int OnShelfQuantity { get; set; }
    
    // Số lượng đang được mượn
    public int BorrowedQuantity { get; set; }
    
    public string? Description { get; set; }
    public string? ImageUrl { get; set; }
    public decimal? Price { get; set; }
    
    // Shelf location (simplified)
    public int? BookshelfId { get; set; }
    public string? LocationCode { get; set; } // Simple location like "A1", "B2"

    // Navigation properties
    public virtual Category Category { get; set; } = null!;
    public virtual Bookshelf? Bookshelf { get; set; }
    public virtual ICollection<BorrowRecord> BorrowRecords { get; set; } = new List<BorrowRecord>();

    // Phương thức để thêm sách vào kho
    public void AddToStock(int quantity)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be positive", nameof(quantity));
            
        StockQuantity += quantity;
    }
    
    // Phương thức để chuyển sách từ kho lên kệ
    public bool MoveToShelf(int quantity, int bookshelfId, string locationCode)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be positive", nameof(quantity));
            
        if (StockQuantity < quantity)
            return false; // Không đủ sách trong kho
            
        StockQuantity -= quantity;
        OnShelfQuantity += quantity;
        BookshelfId = bookshelfId;
        LocationCode = locationCode;
        
        return true;
    }
    
    // Phương thức để mượn sách
    public bool Borrow(int quantity)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be positive", nameof(quantity));
            
        // Ưu tiên mượn sách từ kệ trước
        if (OnShelfQuantity >= quantity)
        {
            OnShelfQuantity -= quantity;
            BorrowedQuantity += quantity;
            return true;
        }
        else if (StockQuantity >= quantity)
        {
            // Nếu không đủ trên kệ, mượn từ kho
            StockQuantity -= quantity;
            BorrowedQuantity += quantity;
            return true;
        }
        
        return false; // Không đủ sách để mượn
    }
    
    // Phương thức để trả sách
    public void Return(int quantity = 1)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be positive", nameof(quantity));
            
        if (BorrowedQuantity < quantity)
            throw new InvalidOperationException("Cannot return more books than borrowed");
            
        BorrowedQuantity -= quantity;
        
        // Luôn trả sách vào kho
        StockQuantity += quantity;
    }
}
# Hướng Dẫn Migration Database

Th<PERSON> mục này chứa các script SQL để cập nhật database schema và dữ liệu cho hệ thống quản lý thư viện.

## Các Vấn Đề Đã Được Giải Quyết

1. **Quản lý số lượng sách không nhất quán**: Đ<PERSON> cập nhật Book entity và các phương thức để đảm bảo tính nhất quán giữa `Quantity`, `StockQuantity` và `OnShelfQuantity`.
2. **Thiếu theo dõi sách đã mượn**: Đã thêm trường `BorrowedQuantity` để theo dõi số lượng sách đang được mượn.
3. **Không đồng bộ giữa vị trí sách và số lượng**: <PERSON><PERSON> cập nhật BorrowRecord để lưu thông tin về vị trí sách được mượn.
4. **<PERSON><PERSON><PERSON><PERSON> cơ chế trả một phần**: <PERSON><PERSON> thêm các trường và phương thức để hỗ trợ trả một phần số lượng sách đã mượn.

## Thứ Tự Thực Hiện Migration

1. Chạy script `UpdateSchema.sql` để cập nhật database schema:
   ```
   sqlcmd -S <server> -d LibraryManagementDb -i UpdateSchema.sql
   ```

2. Chạy script `MigrateShelfQuantities.sql` để đồng bộ số lượng sách trên kệ:
   ```
   sqlcmd -S <server> -d LibraryManagementDb -i MigrateShelfQuantities.sql
   ```

3. (Tùy chọn) Chạy script `InsertSampleData.sql` để thêm dữ liệu mẫu phù hợp với cấu trúc mới:
   ```
   sqlcmd -S <server> -d LibraryManagementDb -i InsertSampleData.sql
   ```

4. (Tùy chọn) Tham khảo script `QueryExamples.sql` để xem các ví dụ truy vấn dữ liệu:
   ```
   sqlcmd -S <server> -d LibraryManagementDb -i QueryExamples.sql
   ```

5. Đảm bảo đã deploy phiên bản mới của backend API trước khi tiếp tục sử dụng hệ thống.

## Các Thay Đổi Schema

### Bảng Books
- Thêm trường `BorrowedQuantity` để theo dõi số lượng sách đang được mượn
- Thay đổi `Quantity` thành computed column dựa trên `StockQuantity` + `OnShelfQuantity`

### Bảng BorrowRecords
- Thêm trường `Quantity` để theo dõi số lượng sách mượn trong một lần
- Thêm trường `ReturnedQuantity` để theo dõi số lượng sách đã trả
- Thêm trường `IsPartialReturn` để đánh dấu trạng thái trả một phần
- Thêm trường `BookshelfId` và `LocationCode` để lưu thông tin vị trí sách được mượn

### Enum BorrowStatus
- Thêm giá trị `PartiallyReturned = 6` để đánh dấu trạng thái trả một phần

## Nội Dung Các Script

### UpdateSchema.sql
- Cập nhật cấu trúc database với các trường mới
- Thêm foreign key constraints
- Cập nhật dữ liệu hiện có để đảm bảo tính nhất quán

### MigrateShelfQuantities.sql
- Đồng bộ số lượng sách trên kệ dựa trên dữ liệu từ ShelfSlotBooks
- Cập nhật thông tin vị trí sách trong BorrowRecords
- Thay thế cho migrate-data.html

### InsertSampleData.sql
- Thêm dữ liệu mẫu phù hợp với cấu trúc mới
- Bao gồm Categories, Zones, Bookshelves, Members, Users, Books, và BorrowRecords
- Cập nhật BorrowedQuantity và CurrentCount để đảm bảo tính nhất quán

### QueryExamples.sql
- Các câu truy vấn mẫu để thao tác với dữ liệu
- Thống kê sách theo tình trạng, kệ, thể loại
- Tìm sách quá hạn, sách cần bổ sung
- Thống kê hoạt động mượn/trả
- Stored procedure để cập nhật trạng thái quá hạn

## Lưu Ý

- Sau khi chạy migration, không cần sử dụng file `migrate-data.html` nữa.
- Các phương thức mới trong Book entity và BorrowRecord entity sẽ tự động đảm bảo tính nhất quán của dữ liệu.
- Nếu có lỗi trong quá trình migration, hãy kiểm tra log và thực hiện các bước sửa lỗi tương ứng. 
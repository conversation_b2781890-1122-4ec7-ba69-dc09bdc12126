using LibraryManagement.Core.Entities;

namespace LibraryManagement.Core.Interfaces;

public interface IUnitOfWork : IDisposable
{
    IBookRepository Books { get; }
    IRepository<Category> Categories { get; }
    IRepository<Member> Members { get; }
    IRepository<BorrowRecord> BorrowRecords { get; }
    IRepository<User> Users { get; }
    IRepository<Bookshelf> Bookshelves { get; }
    IRepository<Zone> Zones { get; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task<int> SaveAsync();
    Task BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();

}
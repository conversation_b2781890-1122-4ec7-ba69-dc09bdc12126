-- <PERSON><PERSON><PERSON> thêm dữ liệu mẫu phù hợp với cấu trúc backend hiện tại
-- S<PERSON> dụng sau khi đã cập nhật schema database

USE LibraryManagementDb;
GO

PRINT 'Bắt đầu thêm dữ liệu mẫu...';

-- <PERSON><PERSON><PERSON> dữ liệu hiện có (nế<PERSON> cần)
DELETE FROM BorrowRecords;
DELETE FROM Books;
DELETE FROM Categories;
DELETE FROM Bookshelves;
DELETE FROM Zones;
DELETE FROM Members;
-- <PERSON>hay vì so sánh Role với 'Admin', chúng ta sẽ bỏ qua Users vì có thể gây lỗi khi enum lưu dưới dạng số
-- DELETE FROM Users WHERE Role <> 'Admin'; -- Gi<PERSON> lại tài khoản Admin

-- 1. Thêm Categories (Thể loại)
PRINT 'Thêm dữ liệu Categories...';
SET IDENTITY_INSERT Categories ON;
INSERT INTO Categories (Id, Name, Description, CreatedAt) VALUES
(1, N'Tiểu thuyết', <PERSON>'<PERSON><PERSON><PERSON> tác phẩm văn học hư cấu', GETDATE()),
(2, N'Khoa học', N'Sách khoa học và nghiên cứu', GETDATE()),
(3, N'Lịch sử', N'Sách về các sự kiện và nhân vật lịch sử', GETDATE()),
(4, N'<PERSON>ông nghệ', N'Sách về công nghệ và máy tính', GETDATE()),
(5, N'Tâm lý học', N'Sách về tâm lý học và phát triển bản thân', GETDATE()),
(6, N'Kinh tế', N'Sách về kinh tế và kinh doanh', GETDATE()),
(7, N'Thiếu nhi', N'Sách dành cho trẻ em', GETDATE()),
(8, N'Ngoại ngữ', N'Sách học ngoại ngữ', GETDATE());
SET IDENTITY_INSERT Categories OFF;

-- 2. Thêm Zones (Khu vực)
PRINT 'Thêm dữ liệu Zones...';
SET IDENTITY_INSERT Zones ON;
INSERT INTO Zones (Id, Name, Description, CreatedAt) VALUES
(1, N'Khu A', N'Khu vực chính của thư viện', GETDATE()),
(2, N'Khu B', N'Khu vực sách tham khảo', GETDATE()),
(3, N'Khu C', N'Khu vực trẻ em', GETDATE()),
(4, N'Khu D', N'Khu vực nghiên cứu', GETDATE());
SET IDENTITY_INSERT Zones OFF;

-- 3. Thêm Bookshelves (Kệ sách)
PRINT 'Thêm dữ liệu Bookshelves...';
SET IDENTITY_INSERT Bookshelves ON;
INSERT INTO Bookshelves (Id, Name, Description, ZoneId, Capacity, CurrentCount, Status, CreatedAt) VALUES
(1, N'Kệ A1', N'Kệ sách tiểu thuyết', 1, 100, 0, 'Active', GETDATE()),
(2, N'Kệ A2', N'Kệ sách kinh tế', 1, 100, 0, 'Active', GETDATE()),
(3, N'Kệ B1', N'Kệ sách khoa học', 2, 80, 0, 'Active', GETDATE()),
(4, N'Kệ B2', N'Kệ sách công nghệ', 2, 80, 0, 'Active', GETDATE()),
(5, N'Kệ C1', N'Kệ sách thiếu nhi', 3, 60, 0, 'Active', GETDATE()),
(6, N'Kệ D1', N'Kệ sách nghiên cứu', 4, 120, 0, 'Active', GETDATE());
SET IDENTITY_INSERT Bookshelves OFF;

-- 4. Thêm Members (Thành viên)
PRINT 'Thêm dữ liệu Members...';
SET IDENTITY_INSERT Members ON;
INSERT INTO Members (Id, FirstName, LastName, Email, Phone, Address, MembershipDate, Status, CreatedAt) VALUES
(1, N'Nguyễn', N'Văn A', '<EMAIL>', '0901234567', N'123 Đường Lê Lợi, TPHCM', GETDATE(), 1, GETDATE()),
(2, N'Trần', N'Thị B', '<EMAIL>', '0912345678', N'456 Đường Nguyễn Huệ, TPHCM', GETDATE(), 1, GETDATE()),
(3, N'Lê', N'Văn C', '<EMAIL>', '0923456789', N'789 Đường Lê Duẩn, TPHCM', GETDATE(), 1, GETDATE()),
(4, N'Phạm', N'Thị D', '<EMAIL>', '0934567890', N'101 Đường Lý Tự Trọng, TPHCM', GETDATE(), 1, GETDATE()),
(5, N'Hoàng', N'Văn E', '<EMAIL>', '0945678901', N'202 Đường Nguyễn Du, Hà Nội', GETDATE(), 1, GETDATE());
SET IDENTITY_INSERT Members OFF;

-- 5. Thêm Books (Sách) - Với cấu trúc mới (StockQuantity, OnShelfQuantity, BorrowedQuantity)
PRINT 'Thêm dữ liệu Books...';
SET IDENTITY_INSERT Books ON;
INSERT INTO Books (Id, Title, Author, ISBN, Publisher, PublishedDate, CategoryId, StockQuantity, OnShelfQuantity, BorrowedQuantity, Price, Description, ImageUrl, BookshelfId, LocationCode, CreatedAt) VALUES
-- Tiểu thuyết
(1, N'Nhà Giả Kim', N'Paulo Coelho', '9786045991312', N'NXB Văn Học', '2020-01-01', 1, 10, 5, 0, 85000, N'Tiểu thuyết nổi tiếng về hành trình tìm kiếm kho báu', 'https://example.com/book1.jpg', 1, 'A1-01', GETDATE()),
(2, N'Trăm Năm Cô Đơn', N'Gabriel García Márquez', '9786045991329', N'NXB Văn Học', '2019-05-15', 1, 8, 4, 0, 120000, N'Tác phẩm kinh điển của nhà văn đoạt giải Nobel', 'https://example.com/book2.jpg', 1, 'A1-02', GETDATE()),
(3, N'Đắc Nhân Tâm', N'Dale Carnegie', '9786045991336', N'NXB Tổng Hợp', '2018-07-20', 5, 15, 10, 0, 90000, N'Sách về nghệ thuật đối nhân xử thế', 'https://example.com/book3.jpg', 1, 'A1-03', GETDATE()),

-- Khoa học
(4, N'Vũ Trụ Trong Vỏ Hạt Dẻ', N'Stephen Hawking', '9786045991343', N'NXB Trẻ', '2017-11-10', 2, 7, 3, 0, 150000, N'Sách về vũ trụ học và vật lý lý thuyết', 'https://example.com/book4.jpg', 3, 'B1-01', GETDATE()),
(5, N'Súng, Vi Trùng Và Thép', N'Jared Diamond', '9786045991350', N'NXB Thế Giới', '2016-09-25', 2, 6, 4, 0, 180000, N'Lịch sử về sự phát triển của nền văn minh nhân loại', 'https://example.com/book5.jpg', 3, 'B1-02', GETDATE()),

-- Công nghệ
(6, N'Clean Code', N'Robert C. Martin', '9780132350884', 'Prentice Hall', '2008-08-01', 4, 12, 8, 0, 200000, N'Sách về cách viết mã sạch và dễ bảo trì', 'https://example.com/book6.jpg', 4, 'B2-01', GETDATE()),
(7, N'Design Patterns', N'Erich Gamma, Richard Helm, Ralph Johnson, John Vlissides', '9780201633610', 'Addison-Wesley', '1994-10-31', 4, 9, 6, 0, 220000, N'Sách về các mẫu thiết kế phần mềm', 'https://example.com/book7.jpg', 4, 'B2-02', GETDATE()),

-- Kinh tế
(8, N'Nghĩ Giàu Làm Giàu', N'Napoleon Hill', '9786045991367', N'NXB Lao Động', '2020-03-15', 6, 20, 15, 0, 95000, N'Sách kinh điển về thành công và làm giàu', 'https://example.com/book8.jpg', 2, 'A2-01', GETDATE()),
(9, N'Bí Mật Tư Duy Triệu Phú', N'T. Harv Eker', '9786045991374', N'NXB Lao Động', '2019-06-20', 6, 18, 12, 0, 88000, N'Sách về tư duy và thói quen của người giàu', 'https://example.com/book9.jpg', 2, 'A2-02', GETDATE()),

-- Thiếu nhi
(10, N'Dế Mèn Phiêu Lưu Ký', N'Tô Hoài', '9786045991381', N'NXB Kim Đồng', '2018-12-01', 7, 25, 20, 0, 65000, N'Truyện thiếu nhi kinh điển Việt Nam', 'https://example.com/book10.jpg', 5, 'C1-01', GETDATE()),
(11, N'Harry Potter và Hòn Đá Phù Thủy', N'J.K. Rowling', '9786045991398', N'NXB Trẻ', '2020-02-10', 7, 30, 25, 0, 110000, N'Phần đầu tiên trong series Harry Potter', 'https://example.com/book11.jpg', 5, 'C1-02', GETDATE());
SET IDENTITY_INSERT Books OFF;

-- 6. Thêm BorrowRecords (Bản ghi mượn sách) - Với cấu trúc mới (Quantity, ReturnedQuantity, IsPartialReturn)
PRINT 'Thêm dữ liệu BorrowRecords...';

-- Cập nhật số lượng trên kệ trước khi thêm bản ghi mượn
UPDATE Books SET OnShelfQuantity = 3, StockQuantity = 7 WHERE Id = 1; -- Nhà Giả Kim
UPDATE Books SET OnShelfQuantity = 2, StockQuantity = 6 WHERE Id = 2; -- Trăm Năm Cô Đơn
UPDATE Books SET OnShelfQuantity = 8, StockQuantity = 7 WHERE Id = 3; -- Đắc Nhân Tâm
UPDATE Books SET OnShelfQuantity = 2, StockQuantity = 5 WHERE Id = 4; -- Vũ Trụ Trong Vỏ Hạt Dẻ

-- Thêm các bản ghi mượn với các trường mới
SET IDENTITY_INSERT BorrowRecords ON;
INSERT INTO BorrowRecords (Id, BookId, MemberId, BorrowDate, DueDate, ReturnDate, Status, Quantity, ReturnedQuantity, IsPartialReturn, BookshelfId, LocationCode, Notes, CreatedAt) VALUES
-- Sách đã mượn và trả đầy đủ
(1, 1, 1, DATEADD(day, -30, GETDATE()), DATEADD(day, -15, GETDATE()), DATEADD(day, -18, GETDATE()), 2, 2, 2, 0, 1, 'A1-01', N'Mượn và trả đúng hạn', DATEADD(day, -30, GETDATE())),
-- Sách đang mượn, chưa trả
(2, 2, 2, DATEADD(day, -10, GETDATE()), DATEADD(day, 4, GETDATE()), NULL, 1, 2, 0, 0, 1, 'A1-02', N'Đang mượn, chưa đến hạn trả', DATEADD(day, -10, GETDATE())),
-- Sách quá hạn, chưa trả
(3, 4, 3, DATEADD(day, -20, GETDATE()), DATEADD(day, -5, GETDATE()), NULL, 3, 1, 0, 0, 3, 'B1-01', N'Quá hạn trả sách', DATEADD(day, -20, GETDATE())),
-- Sách mượn nhiều, trả một phần
(4, 3, 4, DATEADD(day, -15, GETDATE()), DATEADD(day, 0, GETDATE()), DATEADD(day, -2, GETDATE()), 6, 3, 1, 1, 1, 'A1-03', N'Đã trả 1 cuốn, còn 2 cuốn chưa trả', DATEADD(day, -15, GETDATE())),
-- Sách đã gia hạn
(5, 1, 5, DATEADD(day, -25, GETDATE()), DATEADD(day, 10, GETDATE()), NULL, 5, 2, 0, 0, 1, 'A1-01', N'Đã gia hạn thêm 14 ngày', DATEADD(day, -25, GETDATE()));
SET IDENTITY_INSERT BorrowRecords OFF;

-- 7. Cập nhật BorrowedQuantity cho Books dựa trên BorrowRecords
PRINT 'Cập nhật BorrowedQuantity cho Books...';
UPDATE Books
SET BorrowedQuantity = (
    SELECT ISNULL(SUM(BR.Quantity - BR.ReturnedQuantity), 0)
    FROM BorrowRecords BR
    WHERE BR.BookId = Books.Id
    AND (BR.ReturnDate IS NULL OR BR.IsPartialReturn = 1)
);

-- 8. Cập nhật CurrentCount cho Bookshelves
PRINT 'Cập nhật CurrentCount cho Bookshelves...';
UPDATE BS
SET CurrentCount = (
    SELECT ISNULL(SUM(B.OnShelfQuantity), 0)
    FROM Books B
    WHERE B.BookshelfId = BS.Id
)
FROM Bookshelves BS;

PRINT 'Thêm dữ liệu mẫu hoàn tất!';
GO 
-- Tậ<PERSON> hợp các câu truy vấn SQL mẫu để thao tác với dữ liệu trong database
-- Sử dụng sau khi đã chạy InsertSampleData.sql

USE LibraryManagementDb;
GO

PRINT '===== CÁC CÂU TRUY VẤN MẪU =====';

-- 1. <PERSON><PERSON><PERSON> thị tất cả sách với số lượng chi tiết
SELECT 
    B.Id,
    B.Title,
    B.Author,
    C.Name AS CategoryName,
    B.StockQuantity AS InStock,
    B.OnShelfQuantity AS OnShelf,
    B.BorrowedQuantity AS Borrowed,
    B.Quantity AS TotalQuantity,
    BS.Name AS BookshelfName,
    B.LocationCode
FROM Books B
LEFT JOIN Categories C ON B.CategoryId = C.Id
LEFT JOIN Bookshelves BS ON B.BookshelfId = BS.Id
ORDER BY B.Title;

-- 2. <PERSON><PERSON>n thị sách đang được mượn nhiều nhất
SELECT TOP 5
    B.Title,
    <PERSON>.Author,
    SUM(BR.Quantity - BR.ReturnedQuantity) AS CurrentlyBorrowed
FROM Books B
JOIN BorrowRecords BR ON B.Id = BR.BookId
WHERE (BR.ReturnDate IS NULL OR BR.IsPartialReturn = 1)
GROUP BY B.Id, B.Title, B.Author
ORDER BY CurrentlyBorrowed DESC;

-- 3. Hiển thị danh sách sách quá hạn
SELECT 
    M.FirstName + ' ' + M.LastName AS MemberName,
    B.Title,
    BR.BorrowDate,
    BR.DueDate,
    DATEDIFF(day, BR.DueDate, GETDATE()) AS DaysOverdue,
    BR.Quantity - BR.ReturnedQuantity AS QuantityOverdue,
    COALESCE(BR.Fine, 0) AS Fine
FROM BorrowRecords BR
JOIN Books B ON BR.BookId = B.Id
JOIN Members M ON BR.MemberId = M.Id
WHERE BR.DueDate < GETDATE()
AND (BR.ReturnDate IS NULL OR BR.IsPartialReturn = 1)
ORDER BY DaysOverdue DESC;

-- 4. Hiển thị thống kê mượn sách theo thể loại
SELECT 
    C.Name AS CategoryName,
    COUNT(BR.Id) AS TotalBorrows,
    SUM(BR.Quantity) AS TotalQuantityBorrowed,
    SUM(CASE WHEN BR.ReturnDate IS NULL OR BR.IsPartialReturn = 1 THEN BR.Quantity - BR.ReturnedQuantity ELSE 0 END) AS CurrentlyBorrowed
FROM Categories C
LEFT JOIN Books B ON C.Id = B.CategoryId
LEFT JOIN BorrowRecords BR ON B.Id = BR.BookId
GROUP BY C.Id, C.Name
ORDER BY TotalBorrows DESC;

-- 5. Hiển thị lịch sử mượn/trả của một thành viên
DECLARE @MemberId INT = 1; -- Thay đổi ID thành viên tùy ý

SELECT 
    B.Title,
    BR.BorrowDate,
    BR.DueDate,
    BR.ReturnDate,
    BR.Quantity,
    BR.ReturnedQuantity,
    CASE 
        WHEN BR.Status = 1 THEN N'Đang mượn'
        WHEN BR.Status = 2 THEN N'Đã trả'
        WHEN BR.Status = 3 THEN N'Quá hạn'
        WHEN BR.Status = 4 THEN N'Mất sách'
        WHEN BR.Status = 5 THEN N'Đã gia hạn'
        WHEN BR.Status = 6 THEN N'Trả một phần'
        ELSE N'Không xác định'
    END AS StatusName,
    BR.Fine
FROM BorrowRecords BR
JOIN Books B ON BR.BookId = B.Id
WHERE BR.MemberId = @MemberId
ORDER BY BR.BorrowDate DESC;

-- 6. Hiển thị sách có sẵn để mượn (số lượng trong kho + trên kệ > 0)
SELECT 
    B.Id,
    B.Title,
    B.Author,
    C.Name AS CategoryName,
    B.StockQuantity + B.OnShelfQuantity AS AvailableQuantity,
    CASE 
        WHEN B.BookshelfId IS NOT NULL THEN BS.Name + ' - ' + B.LocationCode
        ELSE 'Trong kho'
    END AS Location
FROM Books B
JOIN Categories C ON B.CategoryId = C.Id
LEFT JOIN Bookshelves BS ON B.BookshelfId = BS.Id
WHERE B.StockQuantity + B.OnShelfQuantity > 0
ORDER BY B.Title;

-- 7. Thống kê số lượng sách theo kệ
SELECT 
    BS.Name AS BookshelfName,
    Z.Name AS ZoneName,
    C.Name AS CategoryName,
    COUNT(B.Id) AS NumberOfBooks,
    SUM(B.OnShelfQuantity) AS TotalBooksOnShelf,
    BS.Capacity,
    BS.Capacity - SUM(B.OnShelfQuantity) AS RemainingCapacity
FROM Bookshelves BS
LEFT JOIN Books B ON BS.Id = B.BookshelfId
LEFT JOIN Zones Z ON BS.ZoneId = Z.Id
LEFT JOIN Categories C ON BS.CategoryId = C.Id
GROUP BY BS.Id, BS.Name, Z.Name, C.Name, BS.Capacity
ORDER BY Z.Name, BS.Name;

-- 8. Tìm các thành viên có sách quá hạn
SELECT 
    M.Id,
    M.FirstName + ' ' + M.LastName AS MemberName,
    M.Email,
    M.Phone,
    COUNT(BR.Id) AS OverdueItems,
    SUM(BR.Quantity - BR.ReturnedQuantity) AS OverdueQuantity,
    MAX(DATEDIFF(day, BR.DueDate, GETDATE())) AS MaxDaysOverdue,
    SUM(COALESCE(BR.Fine, 0)) AS TotalFines
FROM Members M
JOIN BorrowRecords BR ON M.Id = BR.MemberId
WHERE BR.DueDate < GETDATE()
AND (BR.ReturnDate IS NULL OR BR.IsPartialReturn = 1)
GROUP BY M.Id, M.FirstName, M.LastName, M.Email, M.Phone
ORDER BY MaxDaysOverdue DESC;

-- 9. Thống kê hoạt động mượn/trả theo tháng
SELECT 
    YEAR(BR.BorrowDate) AS Year,
    MONTH(BR.BorrowDate) AS Month,
    COUNT(BR.Id) AS TotalBorrows,
    SUM(BR.Quantity) AS TotalQuantityBorrowed,
    SUM(CASE WHEN BR.ReturnDate IS NOT NULL THEN 1 ELSE 0 END) AS TotalReturns,
    SUM(CASE WHEN BR.ReturnDate IS NOT NULL THEN BR.ReturnedQuantity ELSE 0 END) AS TotalQuantityReturned,
    SUM(COALESCE(BR.Fine, 0)) AS TotalFines
FROM BorrowRecords BR
GROUP BY YEAR(BR.BorrowDate), MONTH(BR.BorrowDate)
ORDER BY Year DESC, Month DESC;

-- 10. Tìm sách cần bổ sung (số lượng sẵn có thấp)
SELECT 
    B.Id,
    B.Title,
    B.Author,
    C.Name AS CategoryName,
    B.StockQuantity + B.OnShelfQuantity AS AvailableQuantity,
    B.BorrowedQuantity,
    B.Quantity AS TotalQuantity
FROM Books B
JOIN Categories C ON B.CategoryId = C.Id
WHERE (B.StockQuantity + B.OnShelfQuantity) < 3
AND B.BorrowedQuantity > 0
ORDER BY AvailableQuantity ASC;

-- 11. Thống kê số lượng trả một phần
SELECT 
    B.Title,
    BR.BorrowDate,
    BR.Quantity AS OriginalQuantity,
    BR.ReturnedQuantity,
    BR.Quantity - BR.ReturnedQuantity AS RemainingQuantity,
    M.FirstName + ' ' + M.LastName AS MemberName
FROM BorrowRecords BR
JOIN Books B ON BR.BookId = B.Id
JOIN Members M ON BR.MemberId = M.Id
WHERE BR.IsPartialReturn = 1
ORDER BY BR.BorrowDate DESC;

-- 12. Thống kê tổng số sách theo tình trạng
SELECT
    SUM(StockQuantity) AS TotalInStock,
    SUM(OnShelfQuantity) AS TotalOnShelf,
    SUM(BorrowedQuantity) AS TotalBorrowed,
    SUM(StockQuantity + OnShelfQuantity) AS TotalAvailable,
    SUM(StockQuantity + OnShelfQuantity + BorrowedQuantity) AS GrandTotal
FROM Books;

-- 13. Tìm sách chưa từng được mượn
SELECT 
    B.Id,
    B.Title,
    B.Author,
    C.Name AS CategoryName,
    B.StockQuantity + B.OnShelfQuantity AS AvailableQuantity
FROM Books B
JOIN Categories C ON B.CategoryId = C.Id
LEFT JOIN BorrowRecords BR ON B.Id = BR.BookId
WHERE BR.Id IS NULL
ORDER BY B.Title;

-- 14. Thống kê thành viên mượn nhiều sách nhất
SELECT TOP 5
    M.Id,
    M.FirstName + ' ' + M.LastName AS MemberName,
    COUNT(BR.Id) AS TotalBorrows,
    SUM(BR.Quantity) AS TotalQuantityBorrowed,
    SUM(CASE WHEN BR.ReturnDate IS NULL OR BR.IsPartialReturn = 1 THEN BR.Quantity - BR.ReturnedQuantity ELSE 0 END) AS CurrentlyBorrowed
FROM Members M
JOIN BorrowRecords BR ON M.Id = BR.MemberId
GROUP BY M.Id, M.FirstName, M.LastName
ORDER BY TotalQuantityBorrowed DESC;

-- 15. Tạo stored procedure để cập nhật trạng thái quá hạn
IF OBJECT_ID('UpdateOverdueStatus', 'P') IS NOT NULL
    DROP PROCEDURE UpdateOverdueStatus;
GO

CREATE PROCEDURE UpdateOverdueStatus
AS
BEGIN
    -- Cập nhật trạng thái quá hạn cho các bản ghi mượn
    UPDATE BorrowRecords
    SET Status = 3 -- Overdue
    WHERE DueDate < GETDATE()
    AND (ReturnDate IS NULL OR IsPartialReturn = 1)
    AND Status <> 3;
    
    -- Tính toán tiền phạt cho các bản ghi quá hạn
    UPDATE BorrowRecords
    SET Fine = CASE 
                WHEN Fine IS NULL THEN DATEDIFF(day, DueDate, GETDATE()) * 5000 * (Quantity - ReturnedQuantity)
                ELSE Fine
              END
    WHERE DueDate < GETDATE()
    AND (ReturnDate IS NULL OR IsPartialReturn = 1)
    AND Status = 3;
    
    SELECT @@ROWCOUNT AS UpdatedRecords;
END
GO

-- Chạy stored procedure
EXEC UpdateOverdueStatus;
GO 
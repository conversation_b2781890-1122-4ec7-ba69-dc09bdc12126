using Microsoft.EntityFrameworkCore.Storage;
using LibraryManagement.Core.Entities;
using LibraryManagement.Core.Interfaces;
using LibraryManagement.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace LibraryManagement.Infrastructure.Repositories;

public class UnitOfWork : IUnitOfWork
{
    private readonly LibraryDbContext _context;
    private IDbContextTransaction? _transaction;

    private IBookRepository? _books;
    private IRepository<Category>? _categories;
    private IRepository<Member>? _members;
    private IRepository<BorrowRecord>? _borrowRecords;
    private IRepository<User>? _users;
    private IRepository<Bookshelf>? _bookshelves;
    private IRepository<Zone>? _zones;

    public IRepository<Bookshelf> Bookshelves => _bookshelves ??= new Repository<Bookshelf>(_context);
    public IRepository<Zone> Zones => _zones ??= new Repository<Zone>(_context);

    public UnitOfWork(LibraryDbContext context)
    {
        _context = context;
    }

    public IBookRepository Books => _books ??= new BookRepository(_context);
    public IRepository<Category> Categories => _categories ??= new Repository<Category>(_context);
    public IRepository<Member> Members => _members ??= new Repository<Member>(_context);
    public IRepository<BorrowRecord> BorrowRecords => _borrowRecords ??= new Repository<BorrowRecord>(_context);
    public IRepository<User> Users => _users ??= new Repository<User>(_context);

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }
    public async Task<int> SaveAsync()
    {
        return await _context.SaveChangesAsync();
    }

    public async Task BeginTransactionAsync()
    {
        _transaction = await _context.Database.BeginTransactionAsync();
    }

    public async Task CommitTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.CommitAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context.Dispose();
    }
}
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using LibraryManagement.Core.Interfaces;
using LibraryManagement.Core.Entities;
using LibraryManagement.Application.DTOs;
using System.ComponentModel.DataAnnotations;

namespace LibraryManagement.API.Controllers;

[ApiController]
[Route("api/categories")]
[AllowAnonymous] // Allow anonymous access for all endpoints
public class CategoriesController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public CategoriesController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    // Standard API response wrapper
    private IActionResult ApiResponse<T>(T data, string message = "Success", bool success = true)
    {
        return Ok(new
        {
            success = success,
            message = message,
            data = data
        });
    }

    private IActionResult ApiError(string message, int statusCode = 400, string[] errors = null)
    {
        return StatusCode(statusCode, new
        {
            success = false,
            message = message,
            errors = errors ?? new string[0]
        });
    }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        try
        {
            var categories = await _unitOfWork.Categories
                .Query()
                .Include(c => c.Books)
                .Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    BookCount = c.Books.Count(),
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt,
                    CreatedBy = c.CreatedBy,
                    UpdatedBy = c.UpdatedBy
                })
                .OrderBy(c => c.Name)
                .ToListAsync();

            return Ok(categories);
        }
        catch (Exception ex)
        {
            return ApiError("Lỗi khi tải danh sách thể loại", 500, new[] { ex.Message });
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id)
    {
        try
        {
            var category = await _unitOfWork.Categories
                .Query()
                .Include(c => c.Books)
                .Where(c => c.Id == id)
                .Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    BookCount = c.Books.Count(),
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt,
                    CreatedBy = c.CreatedBy,
                    UpdatedBy = c.UpdatedBy
                })
                .FirstOrDefaultAsync();

            if (category == null)
                return ApiError("Không tìm thấy thể loại", 404);

            return Ok(category);
        }
        catch (Exception ex)
        {
            return ApiError("Lỗi khi tải thông tin thể loại", 500, new[] { ex.Message });
        }
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateCategoryDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToArray();
                return ApiError("Dữ liệu không hợp lệ", 400, errors);
            }

            // Check if category name already exists
            var existingCategory = await _unitOfWork.Categories
                .Query()
                .FirstOrDefaultAsync(c => c.Name.ToLower() == dto.Name.ToLower());

            if (existingCategory != null)
                return ApiError("Tên thể loại đã tồn tại", 409);

            var category = new Category
            {
                Name = dto.Name.Trim(),
                Description = dto.Description?.Trim(),
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.Categories.AddAsync(category);
            await _unitOfWork.SaveAsync();

            var result = new CategoryDto
            {
                Id = category.Id,
                Name = category.Name,
                Description = category.Description,
                BookCount = 0,
                CreatedAt = category.CreatedAt,
                UpdatedAt = category.UpdatedAt,
                CreatedBy = category.CreatedBy,
                UpdatedBy = category.UpdatedBy
            };

            return ApiResponse(result, "Tạo thể loại thành công");
        }
        catch (Exception ex)
        {
            return ApiError("Lỗi khi tạo thể loại", 500, new[] { ex.Message });
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] UpdateCategoryDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToArray();
                return ApiError("Dữ liệu không hợp lệ", 400, errors);
            }

            var category = await _unitOfWork.Categories.GetByIdAsync(id);
            if (category == null)
                return ApiError("Không tìm thấy thể loại", 404);

            // Check if new name conflicts with existing category
            var existingCategory = await _unitOfWork.Categories
                .Query()
                .FirstOrDefaultAsync(c => c.Name.ToLower() == dto.Name.ToLower() && c.Id != id);

            if (existingCategory != null)
                return ApiError("Tên thể loại đã tồn tại", 409);

            category.Name = dto.Name.Trim();
            category.Description = dto.Description?.Trim();
            category.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Categories.UpdateAsync(category);
            await _unitOfWork.SaveAsync();

            return ApiResponse<object>(null, "Cập nhật thể loại thành công");
        }
        catch (Exception ex)
        {
            return ApiError("Lỗi khi cập nhật thể loại", 500, new[] { ex.Message });
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            var category = await _unitOfWork.Categories
                .Query()
                .Include(c => c.Books)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (category == null)
                return ApiError("Không tìm thấy thể loại", 404);

            // Check if category has books
            if (category.Books.Any())
                return ApiError("Không thể xóa thể loại có chứa sách. Vui lòng di chuyển sách sang thể loại khác trước khi xóa.", 400);

            await _unitOfWork.Categories.DeleteAsync(category);
            await _unitOfWork.SaveAsync();

            return ApiResponse<object>(null, "Xóa thể loại thành công");
        }
        catch (Exception ex)
        {
            return ApiError("Lỗi khi xóa thể loại", 500, new[] { ex.Message });
        }
    }

    [HttpGet("{id}/statistics")]
    public async Task<IActionResult> GetStatistics(int id)
    {
        try
        {
            var category = await _unitOfWork.Categories
                .Query()
                .Include(c => c.Books)
                .ThenInclude(b => b.BorrowRecords)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (category == null)
                return ApiError("Không tìm thấy thể loại", 404);

            var statistics = new CategoryStatisticsDto
            {
                Id = category.Id,
                Name = category.Name,
                Description = category.Description,
                TotalBooks = category.Books.Sum(b => b.Quantity),
                AvailableBooks = category.Books.Sum(b => b.StockQuantity),
                BorrowedBooks = category.Books.Sum(b => b.Quantity - b.StockQuantity),
                BooksOnShelves = category.Books.Count(b => b.BookshelfId.HasValue),
                BooksInStorage = category.Books.Count(b => !b.BookshelfId.HasValue),
                CreatedAt = category.CreatedAt,
                UpdatedAt = category.UpdatedAt,
                PopularBooks = category.Books
                    .Select(b => new PopularBookDto
                    {
                        BookId = b.Id,
                        Title = b.Title,
                        Author = b.Author,
                        BorrowCount = b.BorrowRecords.Count(),
                        CategoryName = category.Name
                    })
                    .OrderByDescending(b => b.BorrowCount)
                    .Take(5)
                    .ToList()
            };

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            return ApiError("Lỗi khi tải thống kê thể loại", 500, new[] { ex.Message });
        }
    }

    [HttpGet("search")]
    public async Task<IActionResult> Search([FromQuery] CategorySearchDto searchDto)
    {
        try
        {
            var query = _unitOfWork.Categories
                .Query()
                .Include(c => c.Books)
                .AsQueryable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(searchDto.Query))
            {
                var searchTerm = searchDto.Query.ToLower();
                query = query.Where(c =>
                    c.Name.ToLower().Contains(searchTerm) ||
                    (c.Description != null && c.Description.ToLower().Contains(searchTerm)));
            }

            // Apply sorting
            query = searchDto.SortBy?.ToLower() switch
            {
                "name" => searchDto.SortDirection?.ToLower() == "desc"
                    ? query.OrderByDescending(c => c.Name)
                    : query.OrderBy(c => c.Name),
                "bookcount" => searchDto.SortDirection?.ToLower() == "desc"
                    ? query.OrderByDescending(c => c.Books.Count())
                    : query.OrderBy(c => c.Books.Count()),
                "createdat" => searchDto.SortDirection?.ToLower() == "desc"
                    ? query.OrderByDescending(c => c.CreatedAt)
                    : query.OrderBy(c => c.CreatedAt),
                _ => query.OrderBy(c => c.Name)
            };

            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalCount / searchDto.PageSize);

            var categories = await query
                .Skip((searchDto.Page - 1) * searchDto.PageSize)
                .Take(searchDto.PageSize)
                .Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Description = c.Description,
                    BookCount = c.Books.Count(),
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt,
                    CreatedBy = c.CreatedBy,
                    UpdatedBy = c.UpdatedBy
                })
                .ToListAsync();

            var result = new CategorySearchResultDto
            {
                Categories = categories,
                TotalCount = totalCount,
                Page = searchDto.Page,
                PageSize = searchDto.PageSize,
                TotalPages = totalPages
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            return ApiError("Lỗi khi tìm kiếm thể loại", 500, new[] { ex.Message });
        }
    }

    [HttpPost("validate")]
    public async Task<IActionResult> ValidateCategory([FromBody] CreateCategoryDto dto)
    {
        try
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            // Basic validation
            if (string.IsNullOrWhiteSpace(dto.Name))
                errors.Add("Tên thể loại là bắt buộc");

            if (dto.Name?.Length < 2)
                errors.Add("Tên thể loại phải có ít nhất 2 ký tự");

            if (dto.Name?.Length > 200)
                errors.Add("Tên thể loại không được quá 200 ký tự");

            if (dto.Description?.Length > 1000)
                errors.Add("Mô tả không được quá 1000 ký tự");

            // Check duplicate name
            if (!string.IsNullOrWhiteSpace(dto.Name))
            {
                var existingCategory = await _unitOfWork.Categories
                    .Query()
                    .FirstOrDefaultAsync(c => c.Name.ToLower() == dto.Name.ToLower());

                if (existingCategory != null)
                    errors.Add("Tên thể loại đã tồn tại");
            }

            // Warnings
            if (string.IsNullOrWhiteSpace(dto.Description))
                warnings.Add("Nên thêm mô tả để dễ phân biệt thể loại");

            var result = new CategoryValidationDto
            {
                IsValid = errors.Count == 0,
                Errors = errors,
                Warnings = warnings
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            return ApiError("Lỗi khi kiểm tra dữ liệu", 500, new[] { ex.Message });
        }
    }
}
